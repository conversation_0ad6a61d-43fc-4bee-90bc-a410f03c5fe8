# Talka 响应式设计使用指南

## 目录

1. [概述](#概述)
2. [断点系统](#断点系统)
3. [推荐的简化响应式策略](#推荐的简化响应式策略)
4. [进阶响应式混入](#进阶响应式混入)
5. [CSS变量系统](#CSS变量系统)
6. [JavaScript响应式工具](#JavaScript响应式工具)
7. [PostCSS配置](#PostCSS配置)
8. [最佳实践](#最佳实践)
9. [常见问题](#常见问题)

## 概述

Talka 网站采用了综合响应式设计方案，结合以下技术实现多设备适配：

- **断点系统**：使用预定义的断点来管理不同屏幕尺寸
- **SCSS混入**：使用自定义的响应式混入简化媒体查询编写
- **CSS变量**：使用CSS变量实现断点间的值自动调整
- **PostCSS转换**：使用 postcss-px-to-viewport 实现像素到视口单位的自动转换
- **JavaScript响应式工具**：提供 Vue 组件中使用的响应式断点判断

这种组合方案既能保证元素在不同设备上的比例缩放，又能在需要时通过媒体查询完全改变布局。

## 断点系统

项目使用以下断点值（定义在 `src/styles/abstracts/_responsive.scss`）：

| 断点名称 | 屏幕宽度 | 设备类型 |
|---------|---------|---------|
| small   | 480px   | 小型手机 |
| medium  | 768px   | 大型手机/小型平板 |
| large   | 1024px  | 平板/小型桌面 |
| xlarge  | 1280px  | 桌面 |
| xxlarge | 1440px  | 大型桌面 |

## 推荐的简化响应式策略

**对于大多数组件和页面**，我们推荐采用简化的"二分法"响应式策略：只考虑移动端和桌面端两种情况。这种方法有以下优势：

- ✅ **开发效率更高** - 只需考虑两种布局，减少设计和开发时间
- ✅ **代码量更少** - 不需要为多个断点编写不同的样式
- ✅ **维护更简单** - 只有两种状态需要测试和维护
- ✅ **更一致的用户体验** - 避免了过多中间状态可能带来的不一致

### 基本混入

项目提供两个主要的快捷混入，用于实现简化版响应式：

```scss
// 桌面设备 (≥ 769px)
@include desktop-only {
  display: flex;
  justify-content: space-between;
}

// 移动设备 (≤ 768px)
@include mobile-only {
  display: flex;
  flex-direction: column;
}
```

### 使用示例

#### 1. 移动端优先（推荐）

```scss
.card-container {
  // 移动端默认样式
  display: flex;
  flex-direction: column;
  padding: 16px;

  @include desktop-only {
    // 桌面端覆盖样式
    flex-direction: row;
    padding: 24px;
  }
}
```

#### 2. 桌面端优先

```scss
.header-navigation {
  // 桌面端默认样式
  display: flex;
  justify-content: space-between;

  @include mobile-only {
    // 移动端覆盖样式
    flex-direction: column;
    gap: 16px;
  }
}
```

## 进阶响应式混入

对于需要更精细控制的情况，项目还提供了以下进阶混入：

### 1. 针对特定断点的媒体查询

```scss
// 当屏幕宽度 ≤ 指定断点时应用样式
@include respond-to('medium') {
  // 这里的样式会在宽度 ≤ 768px 的设备上应用
  font-size: 14px;
}
```

### 2. 从小屏幕向上应用

```scss
// 当屏幕宽度 ≥ 指定断点时应用样式
@include respond-from('large') {
  // 这里的样式会在宽度 ≥ 1024px 的设备上应用
  font-size: 18px;
}
```

### 3. 断点范围方法（针对特定范围）

```scss
// 当屏幕宽度在两个断点之间时应用样式
@include respond-between('medium', 'large') {
  // 这里的样式会在 768px ≤ 宽度 ≤ 1024px 的设备上应用
  font-size: 16px;
}
```

### 4. 平板专用

```scss
// 仅平板设备 (769px - 1024px)
@include tablet-only {
  padding: 20px;
}
```

## CSS变量系统

项目使用CSS变量在不同断点下自动调整样式值，定义在 `src/styles/abstracts/_css-variables.scss`。

### 使用方法

1. 在样式中引用CSS变量而不是硬编码值：

```scss
.element {
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
}
```

2. CSS变量会在不同断点自动切换值：

```scss
:root {
  --spacing-md: 16px;
  --font-size-base: 16px;
}

@include desktop-only {
  :root {
    --spacing-md: 24px;
    --font-size-base: 16px;
  }
}
```

### 可用变量列表

| 变量名 | 移动端值 | 桌面端值 | 描述 |
|--------|---------|---------|------|
| --nav-padding | 10px 20px | 10px 40px | 导航区域内边距 |
| --nav-logo-width | 100px | 120px | 导航logo宽度 |
| --nav-link-padding | 14px 16px | 8px 16px | 导航链接内边距 |
| --btn-padding | 10px 16px | 8px 16px | 按钮内边距 |
| --spacing-xs | 8px | 8px | 超小间距 |
| --spacing-sm | 10px | 12px | 小间距 |
| --spacing-md | 16px | 16px | 中间距 |
| --spacing-lg | 20px | 24px | 大间距 |

## JavaScript响应式工具

当组件内部需要基于屏幕尺寸执行 JavaScript 逻辑时，请使用 `src/utils/responsive.js` 中提供的工具而不是在组件内部直接判断窗口尺寸。

### 响应式钩子

```js
import { useResponsive } from '@/utils/responsive';

export default {
  setup() {
    // 获取响应式的设备类型
    const { isMobile, isDesktop } = useResponsive();

    // 用于基于设备类型的条件渲染
    return {
      isMobile,
      isDesktop
    };
  }
}
```

### 自定义断点钩子

```js
import { useBreakpoint, BREAKPOINTS } from '@/utils/responsive';

export default {
  setup() {
    // 获取基于特定断点的响应式判断
    const { isSmallerThanBreakpoint, isLargerThanBreakpoint } =
      useBreakpoint(BREAKPOINTS.large); // 1024px

    return {
      isSmallScreen: isSmallerThanBreakpoint,
      isLargeScreen: isLargerThanBreakpoint
    };
  }
}
```

### 静态断点判断

对于不需要响应式更新的场景，可以使用静态函数：

```js
import { isMobileDevice, isDesktopDevice } from '@/utils/responsive';

// 检查当前是否为移动设备
const isMobile = isMobileDevice();

// 检查当前是否为桌面设备
const isDesktop = isDesktopDevice();
```

### 可用工具列表

| 函数/常量 | 描述 |
|---------|------|
| `BREAKPOINTS` | 预定义的断点常量对象 |
| `useResponsive()` | 返回 `isMobile` 和 `isDesktop` 响应式引用 |
| `useBreakpoint(width)` | 返回特定断点的响应式判断引用 |
| `isMobileDevice()` | 静态函数，判断当前是否为移动设备 |
| `isDesktopDevice()` | 静态函数，判断当前是否为桌面设备 |
| `isSmallerThan(width)` | 静态函数，判断窗口是否小于指定宽度 |
| `isLargerThan(width)` | 静态函数，判断窗口是否大于等于指定宽度 |

## PostCSS配置

项目使用 `postcss-px-to-viewport` 将 px 单位自动转换为 vw 单位，配置在 `postcss.config.js`。

### 关键设置

- **基准宽度**：移动端设计稿宽度为 375px
- **转换属性**：字体大小、内外边距、宽高等
- **排除选择器**：某些不需要转换的选择器使用 `.no-vw` 类

### 使用技巧

- 直接使用设计稿上的 px 值，构建时会自动转换
- 如果不希望某个值被转换，可以：
  - 给元素添加 `.no-vw` 类
  - 使用 CSS 变量代替直接的 px 值
  - 使用 `rem` 或其他单位

## 最佳实践

1. **组件样式导入**

```scss
<style lang="scss" scoped>
@use "../styles/abstracts/variables" as *;
@use "../styles/abstracts/mixins" as *;

// 样式代码
</style>
```

2. **响应式设计策略**

- **简单组件**：只使用 CSS 变量和流式布局，依靠 postcss-px-to-viewport 自动适配
- **中等复杂度**：使用 `mobile-only` 和 `desktop-only` 处理布局差异
- **复杂组件**：在必要时使用进阶混入处理特殊情况

3. **性能优化**

- 尽量减少媒体查询数量，坚持"二分法"（移动/桌面）
- 优先使用 CSS 变量实现响应式，减少生成的 CSS 体积
- 考虑使用 `clamp()`, `min()`, `max()` 等现代 CSS 功能减少媒体查询

## 常见问题

### Q: 样式中找不到响应式混入？

确保正确导入了 mixins：

```scss
@use "../styles/abstracts/mixins" as *;
```

### Q: 某些元素在小屏幕上溢出容器？

检查是否使用了固定宽度而非百分比或视口单位，或者考虑添加：

```scss
max-width: 100%;
overflow-x: hidden;
```

### Q: 桌面样式错误地应用到了移动设备？

检查媒体查询的顺序，确保移动设备的样式放在前面（如果使用移动优先策略）。

### Q: postcss-px-to-viewport 没有转换某些值？

检查该选择器或属性是否在排除列表中，或尝试添加注释标记：

```scss
// 让 postcss-px-to-viewport 处理此行
margin: 10px;
```

---

如有其他问题，请联系项目技术负责人或在 Git 仓库中提交 Issue。
