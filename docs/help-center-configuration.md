# Help Center 动态配置指南

## 概述

Help Center 页面已经重构为支持动态配置的内容区域，不再局限于固定的两个区域（articles-area 和 questions-area）。现在可以通过国际化配置文件灵活地配置任意数量的内容区域。

## 配置方式

### 1. 默认配置（向后兼容）

如果不在国际化文件中配置 `sections`，系统会自动使用默认的两个区域配置，保持向后兼容性。

### 2. 动态配置

在国际化文件中添加 `sections` 配置来自定义内容区域：

```javascript
// src/locales/zh-TW/HelpCenter.js
export default {
  helpCenter: {
    // ... 其他配置
    
    sections: [
      {
        id: 'featured-articles',
        titleKey: 'helpCenter.sectionTitles.featuredArticles',
        flex: 3,
        type: 'articles',
        itemKeys: [
          { titleKey: 'helpCenter.articles.contactUs' },
          { titleKey: 'helpCenter.articles.personalization' },
          { titleKey: 'helpCenter.articles.createInbox' },
        ],
        footer: {
          type: 'stats',
          contentKey: 'helpCenter.footerContent.stats'
        }
      },
      {
        id: 'common-questions',
        titleKey: 'helpCenter.sectionTitles.commonQuestions',
        flex: 2,
        type: 'questions',
        itemKeys: [
          { titleKey: 'helpCenter.questions.contactUs' },
          { titleKey: 'helpCenter.questions.personalization' },
          { titleKey: 'helpCenter.questions.createInbox' },
          { titleKey: 'helpCenter.questions.conversation' },
          { titleKey: 'helpCenter.questions.whatIsTalka' },
        ],
        footer: {
          type: 'viewAll',
          contentKey: 'helpCenter.footerContent.viewAll',
          href: '#'
        }
      },
      {
        id: 'tutorials',
        titleKey: 'helpCenter.sectionTitles.tutorials',
        flex: 2,
        type: 'tutorials',
        itemKeys: [
          { titleKey: 'helpCenter.tutorials.gettingStarted' },
          { titleKey: 'helpCenter.tutorials.advancedFeatures' },
          { titleKey: 'helpCenter.tutorials.troubleshooting' },
        ],
        footer: {
          type: 'custom',
          contentKey: 'helpCenter.footerContent.tutorialNote'
        }
      }
    ]
  }
}
```

## 配置参数说明

### Section 配置

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `id` | string | 是 | 区域的唯一标识符 |
| `titleKey` | string | 是 | 区域标题的国际化键 |
| `flex` | number | 否 | CSS flex 值，控制区域宽度比例（默认为 1） |
| `type` | string | 否 | 区域类型，用于 CSS 类名 |
| `itemKeys` | array | 是 | 区域内链接项的配置数组 |
| `footer` | object | 否 | 底部内容配置 |

### ItemKey 配置

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `titleKey` | string | 是 | 链接文本的国际化键 |
| `href` | string | 否 | 链接地址（默认为 "#"） |

### Footer 配置

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `type` | string | 是 | 底部类型：'stats'、'viewAll'、'custom' |
| `contentKey` | string | 是 | 底部内容的国际化键 |
| `href` | string | 否 | 链接地址（仅 viewAll 类型需要） |

## 底部类型说明

### 1. stats 类型
显示统计信息，带有红色圆点图标。

### 2. viewAll 类型
显示"查看全部"链接，右对齐。

### 3. custom 类型
显示自定义文本内容。

## 样式说明

- 所有区域使用统一的 `.content-area` 类名
- 可以通过 `type` 参数添加特定的类名：`.content-area--{type}`
- 移动端会自动调整为垂直布局
- 通过 `flex` 参数控制桌面端的宽度比例

## 使用示例

要添加第三个区域"API 文档"：

1. 在国际化文件中添加相关翻译
2. 在 `sections` 配置中添加新的区域配置
3. 系统会自动渲染新的区域

这种设计使得 Help Center 页面具有高度的灵活性和可扩展性。
