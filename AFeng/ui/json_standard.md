<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-24 18:46:44
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-23 12:08:51
 * @FilePath     : /AFeng/ui/json_standard.md
 * @Description  : UI组件JSON数据格式统一标准
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-24 18:46:44
-->

# UI 组件 JSON 数据格式标准

## 文档说明

本文档定义了 UI 设计分析到代码生成的统一 JSON 数据格式标准。

**适用范围**：

- UI 设计图分析输出 (`ui_design_analyzer.md`)
- 代码生成工具输入 (`ui_to_code.md`)
- 其他 UI 相关数据处理工具

**核心原则**：

- **智能化字段** - 只生成设计图中实际存在的样式
- **条件化生成** - 根据元素类型和布局方式生成对应字段
- **类型安全** - 明确每个字段的数据类型和取值范围
- **映射标准** - 提供 JSON 到 CSS 的精确映射关系

## 标准 JSON 格式

### 核心版本（推荐使用）

```json
{
  "componentName": "组件名称（必填）",
  "containerStyles": {
    "display": "flex|grid|block（必填）",

    // 条件字段：仅当 display 为 flex 时才包含
    "flexDirection": "row|column（仅当display=flex时）",
    "justifyContent": "flex-start|center|flex-end|space-between|space-around（仅当display=flex时）",
    "alignItems": "flex-start|center|flex-end|stretch（仅当display=flex时）",
    "gap": "间距值（仅当display=flex或grid时，如16px）",

    // 条件字段：仅当 display 为 grid 时才包含
    "gridTemplateColumns": "列定义（仅当display=grid时，如repeat(3, 1fr)）",
    "gridTemplateRows": "行定义（仅当display=grid时，如auto 1fr）",

    // 条件字段：仅当使用定位布局时才包含
    "position": "relative|absolute|fixed（仅当layout.type=position时）",

    // 通用字段：按实际设计情况包含
    "width": "宽度（设计中有明确宽度时，如100%、300px）",
    "height": "高度（设计中有明确高度时，如auto、237px）",
    "backgroundColor": "背景色（设计中有背景色时，如#262626）",
    "borderRadius": "圆角（设计中有圆角时，如12px）",
    "padding": "内边距（设计中有内边距时，如16px）",
    "margin": "外边距（设计中有外边距时，如0 auto）",
    "border": "边框（设计中有边框时，如1px solid #ccc）",
    "boxShadow": "阴影（设计中有阴影时，如0 2px 4px rgba(0,0,0,0.1)）"
  },
  "layout": {
    "type": "flex|grid|position|block（必填）",
    "direction": "row|column|none（必填，position/block时为none）",
    "areas": [
      {
        "name": "区域名称（必填，camelCase，如timeSection）",
        "className": "CSS类名（必填，kebab-case，如time-section）",
        "layout": "区域布局方式（必填，见布局映射表）",
        "elements": [
          {
            "type": "text|image|button|container|badge|icon（必填）",
            "name": "元素名称（必填，camelCase，如title）",
            "className": "CSS类名（必填，kebab-case，如title）",

            // 条件字段：仅当 type 为 text 时包含
            "content": "文本内容（仅当type=text时必填）",

            // 条件字段：仅当 type 为 image 时包含
            "src": "图片地址（仅当type=image时必填）",
            "alt": "图片描述（仅当type=image时）",

            "styles": {
              // 文字相关样式：仅当元素包含文字时才包含
              "fontSize": "字体大小（仅当有文字时，如16px）",
              "fontWeight": "字重（仅当有文字时，如600、bold）",
              "fontFamily": "字体族（仅当有文字时，如Arial, sans-serif）",
              "lineHeight": "行高（仅当有文字时，如1.5）",
              "textAlign": "文本对齐（仅当有文字时，如center）",
              "color": "文字颜色（仅当有文字时，如#ffffff）",

              // 尺寸样式：仅当设计中明确指定时才包含
              "width": "宽度（设计中有明确宽度时，如64px、100%）",
              "height": "高度（设计中有明确高度时，如64px、auto）",

              // 背景样式：仅当设计中有背景时才包含
              "backgroundColor": "背景色（设计中有背景色时，如#3a3a3a）",

              // 边框圆角：仅当设计中有圆角时才包含
              "borderRadius": "圆角（设计中有圆角时，如8px、50%）",

              // 间距样式：仅当设计中有间距时才包含
              "padding": "内边距（设计中有内边距时，如8px 12px）",
              "margin": "外边距（设计中有外边距时，如8px 0）",

              // 定位样式：仅当使用绝对定位时才包含
              "position": "定位方式（仅当需要特殊定位时，如absolute）",
              "top": "顶部位置（仅当position=absolute时，如10px）",
              "left": "左侧位置（仅当position=absolute时，如50%）",
              "right": "右侧位置（仅当position=absolute时，如10px）",
              "bottom": "底部位置（仅当position=absolute时，如10px）",
              "zIndex": "层级（仅当需要层级控制时，如10）",

              // 图片样式：仅当 type 为 image 时才包含
              "objectFit": "图片适应（仅当type=image时，如cover、contain）",

              // 变换样式：仅当设计中有变换效果时才包含
              "transform": "变换（仅当有旋转、缩放等效果时，如rotate(-45deg)）"
            }
          }
        ]
      }
    ]
  },
  "colors": {
    "primary": "主色（必填，如#17d400）",
    "text": "主要文字色（必填，如#ffffff）",
    "background": "主要背景色（必填，如#262626）",

    // 以下颜色：仅当设计中实际使用时才包含
    "textSecondary": "次要文字色（设计中有次要文字时，如#a0a0a0）",
    "backgroundSecondary": "次要背景色（设计中有次要背景时，如#3a3a3a）",
    "border": "边框色（设计中有边框时，如#454545）",
    "accent": "强调色（设计中有强调色时，如#ff6b35）"
  }
}
```

### 完整版本（高级功能）

<details>
<summary>点击展开完整版本（包含响应式、动画、状态等高级功能）</summary>

```json
{
  "componentName": "组件名称",
  "containerStyles": { /* 基础容器样式 */ },
  "layout": { /* 基础布局结构 */ },
  "colors": { /* 基础颜色系统 */ },
  "typography": {
    "fontFamily": "字体族（如Arial, sans-serif）",
    "sizes": {
      "xs": "超小字体（如10px）",
      "sm": "小字体（如12px）",
      "md": "中字体（如16px）",
      "lg": "大字体（如20px）",
      "xl": "超大字体（如28px）"
    },
    "weights": {
      "normal": "正常字重（如400）",
      "medium": "中等字重（如500）",
      "semibold": "半粗（如600）",
      "bold": "粗体（如700）"
    }
  },
  "responsive": {
    "mobile": {
      "breakpoint": "768px",
      "containerStyles": { "移动端样式覆盖" },
      "areas": [
        {
          "name": "区域名（与主layout对应）",
          "styles": { "移动端区域样式覆盖" },
          "elements": [
            {
              "name": "元素名（与主layout对应）",
              "styles": { "移动端元素样式覆盖" }
            }
          ]
        }
      ]
    }
  },
  "states": {
    "hover": { "悬停状态样式" },
    "active": { "激活状态样式" },
    "focus": { "焦点状态样式" }
  },
  "animations": {
    "transitions": {
      "default": "all 0.3s ease",
      "fast": "all 0.15s ease"
    }
  }
}
```

</details>

## 字段映射规则

### 布局类型映射

| 视觉布局识别 | JSON 中 layout.type | JSON 中 layout.direction | 对应 CSS 输出                           |
| ------------ | ------------------- | ------------------------ | --------------------------------------- |
| 水平排列     | `"flex"`            | `"row"`                  | `display: flex; flex-direction: row`    |
| 垂直排列     | `"flex"`            | `"column"`               | `display: flex; flex-direction: column` |
| 网格排列     | `"grid"`            | `"row"` 或 `"column"`    | `display: grid`                         |
| 绝对定位布局 | `"position"`        | `"none"`                 | `position: absolute/relative/fixed`     |
| 普通文档流   | `"block"`           | `"none"`                 | `display: block`                        |

### 区域布局映射

| 区域布局描述 | JSON 中的 layout 值        | 对应 CSS 输出                                                 |
| ------------ | -------------------------- | ------------------------------------------------------------- |
| 水平 Flexbox | `"flex-horizontal"`        | `display: flex; flex-direction: row`                          |
| 垂直 Flexbox | `"flex-vertical"`          | `display: flex; flex-direction: column`                       |
| 垂直居中     | `"flex-vertical-center"`   | `display: flex; flex-direction: column; align-items: center`  |
| 水平居中     | `"flex-horizontal-center"` | `display: flex; flex-direction: row; justify-content: center` |
| 完全居中     | `"flex-center"`            | `display: flex; justify-content: center; align-items: center` |
| 网格布局     | `"grid"`                   | `display: grid`                                               |
| 相对定位     | `"relative"`               | `position: relative`                                          |
| 绝对定位     | `"absolute"`               | `position: absolute`                                          |

### 元素类型映射

| 元素类型 | JSON 中的 type 值 | 对应 HTML 标签         | 必须字段             | 可选字段                     | 不应包含                   |
| -------- | ----------------- | ---------------------- | -------------------- | ---------------------------- | -------------------------- |
| 文本内容 | `"text"`          | `<div>` `<span>` `<p>` | `content`, 文字样式  | 盒模型样式                   | `src`, `alt`, `objectFit`  |
| 标题文本 | `"heading"`       | `<h1>` - `<h6>`        | `content`, 文字样式  | 盒模型样式                   | `src`, `alt`, `objectFit`  |
| 图片     | `"image"`         | `<img>`                | `src`                | `alt`, `objectFit`, 尺寸样式 | `content`, 文字样式        |
| 按钮     | `"button"`        | `<button>`             | 文字样式, 背景样式   | 状态样式                     | `src`, `objectFit`         |
| 容器     | `"container"`     | `<div>`                | 盒模型样式           | 布局样式                     | `content`, `src`, 文字样式 |
| 徽章标签 | `"badge"`         | `<div>`                | 背景、圆角、文字样式 | 定位样式                     | `src`, `objectFit`         |
| 图标     | `"icon"`          | `<i>` `<svg>`          | 尺寸样式             | 颜色样式                     | `content`, `src`           |

## 条件化字段规则

### 1. 布局相关字段条件

| 条件             | 应包含的字段                                           | 不应包含的字段                                  |
| ---------------- | ------------------------------------------------------ | ----------------------------------------------- |
| `type: flex`     | `flexDirection`, `justifyContent`, `alignItems`, `gap` | `gridTemplateColumns`, `gridTemplateRows`       |
| `type: grid`     | `gridTemplateColumns`, `gridTemplateRows`, `gap`       | `flexDirection`, `justifyContent`, `alignItems` |
| `type: position` | `position`, 基础盒模型样式                             | flex 和 grid 相关字段                           |
| `type: block`    | 基础盒模型样式                                         | flex 和 grid 相关字段                           |

### 2. 样式存在性检查

- **文字样式** (`fontSize`, `fontWeight`, `color`, `textAlign` 等)

  - ✅ 包含条件：元素 type 为 text、heading、button 或有 content 字段
  - ❌ 排除条件：元素 type 为 image、icon、container

- **图片样式** (`objectFit`, `alt` 等)

  - ✅ 包含条件：元素 type 为 image
  - ❌ 排除条件：其他所有 type

- **定位样式** (`position`, `top`, `left`, `right`, `bottom`, `zIndex`)

  - ✅ 包含条件：设计中元素使用绝对定位或需要层级控制
  - ❌ 排除条件：普通文档流中的元素

- **背景样式** (`backgroundColor`)

  - ✅ 包含条件：设计图中明确显示有背景色
  - ❌ 排除条件：透明或继承背景的元素

- **圆角样式** (`borderRadius`)
  - ✅ 包含条件：设计图中明确显示有圆角
  - ❌ 排除条件：方形元素

## 数据类型规范

### 基本数据类型

- **字符串类型** - 所有 CSS 属性值（包含单位，如"16px", "100%", "#ffffff"）
- **数字类型** - 仅用于特殊属性（如 zIndex: 10）
- **数组类型** - 仅用于 areas 和 elements
- **对象类型** - 用于嵌套结构（styles, colors 等）

### 命名规范

- **区域 name** - 使用 camelCase（如"timeRemainingSection"）
- **元素 name** - 使用 camelCase（如"countdownTimer"）
- **className** - 使用 kebab-case（如"time-remaining-section"）
- **颜色值** - 使用十六进制格式（如"#262626"）
- **尺寸值** - 必须包含单位（如"16px", "100%", "1.5em"）

## 验证检查清单

### 基础检查

- [ ] 所有必填字段都已填写
- [ ] layout.type 和 layout.direction 的组合有效
- [ ] 每个区域的 layout 值在映射表中存在
- [ ] 每个元素的 type 值在映射表中存在
- [ ] 所有 CSS 属性值都包含正确的单位
- [ ] 颜色值格式正确（#rrggbb）
- [ ] 类名命名符合规范（camelCase/kebab-case）

### 条件性字段检查

- [ ] **布局字段**：flex 相关字段只在`type: flex`时出现
- [ ] **布局字段**：grid 相关字段只在`type: grid`时出现
- [ ] **布局字段**：position 和 block 类型不包含 flex/grid 字段
- [ ] **元素字段**：`content`字段只在 text 类型时出现
- [ ] **元素字段**：`src`字段只在 image 类型时出现
- [ ] **样式字段**：文字样式只在有文字内容的元素中出现
- [ ] **样式字段**：图片样式只在图片元素中出现
- [ ] **样式字段**：定位样式只在需要特殊定位时出现
- [ ] **颜色字段**：次要颜色只在设计中实际使用时出现

### 逻辑一致性检查

- [ ] 元素类型与内容字段匹配（text 有 content，image 有 src）
- [ ] 容器的 display 类型与子元素布局方式兼容
- [ ] 绝对定位元素包含必要的位置属性
- [ ] 样式字段与元素的视觉表现一致
- [ ] 嵌套结构层次清晰且合理

## 解析说明

### 核心原理

- **直接映射** - JSON 属性名对应 CSS 属性名
- **条件解析** - 根据元素类型和布局方式生成对应字段
- **结构映射** - JSON 嵌套结构对应 HTML DOM 结构

### 基本示例

```json
// JSON输入
{
  "containerStyles": {
    "display": "flex",
    "gap": "16px",
    "padding": "16px"
  }
}
```

```scss
// SCSS输出
.container {
  display: flex;
  gap: 16px;
  padding: 16px;
}
```

### 条件字段规则

- **flex 布局** - 生成 flexDirection, justifyContent 等字段
- **grid 布局** - 生成 gridTemplateColumns, gridTemplateRows 等字段
- **text 元素** - 生成 fontSize, color 等文字样式
- **image 元素** - 生成 objectFit, alt 等图片属性
- **其他类型** - 按映射表生成对应样式

## 实际应用示例

### ✅ 正确示例

```json
{
  "componentName": "UserCard",
  "containerStyles": {
    "display": "flex",
    "flexDirection": "row",
    "gap": "16px",
    "padding": "16px",
    "backgroundColor": "#ffffff",
    "borderRadius": "8px"
  },
  "layout": {
    "type": "flex",
    "direction": "row",
    "areas": [
      {
        "name": "avatarSection",
        "className": "avatar-section",
        "layout": "flex-center",
        "elements": [
          {
            "type": "image",
            "name": "userAvatar",
            "className": "user-avatar",
            "src": "avatar.jpg",
            "alt": "用户头像",
            "styles": {
              "width": "64px",
              "height": "64px",
              "borderRadius": "50%"
            }
          }
        ]
      },
      {
        "name": "infoSection",
        "className": "info-section",
        "layout": "flex-vertical",
        "elements": [
          {
            "type": "text",
            "name": "userName",
            "className": "user-name",
            "content": "张三",
            "styles": {
              "fontSize": "18px",
              "fontWeight": "600",
              "color": "#333333"
            }
          }
        ]
      }
    ]
  },
  "colors": {
    "primary": "#007bff",
    "text": "#333333",
    "background": "#ffffff"
  }
}
```

### ❌ 错误示例

```json
{
  "type": "image",
  "styles": {
    "fontSize": "16px", // ❌ 图片不需要字体样式
    "color": "#ffffff", // ❌ 图片不需要文字颜色
    "gap": "8px", // ❌ 单个元素不需要gap
    "flexDirection": "row" // ❌ 元素样式中不应有布局属性
  }
}
```

---

**版本信息**：v1.0
**更新日期**：2025-06-24
**维护者**：Bruce
