/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-24 14:02:24
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 14:05:28
 * @FilePath     : /postcss.config.js
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-24 14:02:24
 */
export default {
  plugins: {
    "postcss-px-to-viewport": {
      // 基于移动端设计稿宽度
      viewportWidth: 375,
      viewportHeight: 667,
      unitPrecision: 3,
      viewportUnit: "vw",

      // 不转换的选择器（保持原有布局结构）
      selectorBlackList: [
        ".fixed-", // 固定定位元素
        ".no-vw", // 不需要转换的元素添加 .no-vw 类
        ":root", // CSS 变量声明不转换
        "html", // 根元素不转换
        "body", // 根元素不转换
      ],

      // 只转换这些属性（精确控制）
      propList: [
        "font-size",
        "padding*",
        "margin*",
        "width",
        "height",
        "border-radius",
        "border-width",
        "line-height",
        "letter-spacing",
        "top",
        "right",
        "bottom",
        "left",
        "gap",
      ],

      // 不转换的属性
      propBlackList: [
        "max-width", // 最大宽度保持固定
        "min-width", // 最小宽度保持固定
        "max-height", // 最大高度保持固定
        "min-height", // 最小高度保持固定
        "border", // 边框保持固定
        "box-shadow", // 阴影保持固定
        "transform", // 变换保持固定
        "z-index", // 层级保持固定
      ],

      minPixelValue: 1,
      mediaQuery: true, // 转换媒体查询中的px

      // 排除文件
      exclude: [/node_modules/, /\.min\./, /abstracts\/_css-variables\.scss/],

      landscape: false,
      landscapeUnit: "vw",
      landscapeWidth: 568,
    },
    autoprefixer: {},
  },
};
