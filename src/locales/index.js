/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-17 20:17:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 16:31:53
 * @FilePath     : /src/locales/index.js
 * @Description  : i18n configuration
 * Copyright 2025 Bruce, All Rights Reserved.
 */

import { createI18n } from "vue-i18n";
import en from "./en";
import zhTW from "./zh-TW";
import { getInitialLanguage } from "@/utils/language";

const initialLanguage = getInitialLanguage();

const i18n = createI18n({
  legacy: false, // 使用组合式API
  globalInjection: true, // 全局注入 $t 函数
  locale: initialLanguage, // 使用检测到的语言
  fallbackLocale: "en", // 回退语言
  messages: {
    en,
    "zh-TW": zhTW,
  },
  // 生产环境配置
  silentTranslationWarn: true,
  missingWarn: false,
  fallbackWarn: false,
});

export default i18n;
