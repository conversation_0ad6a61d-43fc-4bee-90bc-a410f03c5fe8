/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-17 20:17:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-28 16:42:56
 * @FilePath     : /src/locales/zh-TW/index.js
 * @Description  : Traditional Chinese language entry file
 * Copyright 2025 Bruce, All Rights Reserved.
 */

import AnnouncementBar from "./AnnouncementBar.js";
import CallRecording from "./CallRecording.js";
import CallToAction from "./CallToAction.js";
import ContactModal from "./ContactModal.js";
import Demo from "./Demo.js";
import Features from "./Features.js";
import Footer from "./Footer.js";
import GlobalTeam from "./GlobalTeam.js";
import Hero from "./Hero.js";
import How from "./How.js";
import InfoCenter from "./InfoCenter.js";
import LanguageSwitcher from "./LanguageSwitcher.js";
import Navbar from "./Navbar.js";
import Price from "./Price.js";
import VideoEmbed from "./VideoEmbed.js";
import common from "./common.js";

export default {
  ...AnnouncementBar,
  ...CallRecording,
  ...CallToAction,
  ...ContactModal,
  ...Demo,
  ...Features,
  ...Footer,
  ...GlobalTeam,
  ...Hero,
  ...How,
  ...InfoCenter,
  ...LanguageSwitcher,
  ...Navbar,
  ...Price,
  ...VideoEmbed,
  ...common,
  helpCenter: {
    searchResults: {
      found: "找到 {count} 个相关结果",
      notFound: '未找到与 "{query}" 相关的内容',
    },
  },
};
