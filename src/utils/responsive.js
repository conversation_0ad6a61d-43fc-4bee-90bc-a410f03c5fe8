import { ref, onMounted, onUnmounted } from "vue";

/**
 * 断点定义 - 与 SCSS 中定义保持一致
 */
export const BREAKPOINTS = {
  small: 480,
  medium: 768,
  large: 1024,
  xlarge: 1280,
  xxlarge: 1440,
};

/**
 * 检查当前窗口宽度是否小于指定断点
 * @param {number} width - 断点宽度
 * @returns {boolean} - 是否小于指定断点
 */
export const isSmallerThan = (width) => {
  return window.innerWidth < width;
};

/**
 * 检查当前窗口宽度是否大于指定断点
 * @param {number} width - 断点宽度
 * @returns {boolean} - 是否大于指定断点
 */
export const isLargerThan = (width) => {
  return window.innerWidth >= width;
};

/**
 * 检查当前窗口是否为移动设备
 * @returns {boolean} - 是否为移动设备（小于768px）
 */
export const isMobileDevice = () => {
  return isSmallerThan(BREAKPOINTS.medium);
};

/**
 * 检查当前窗口是否为桌面设备
 * @returns {boolean} - 是否为桌面设备（大于等于768px）
 */
export const isDesktopDevice = () => {
  return isLargerThan(BREAKPOINTS.medium);
};

/**
 * 创建响应式的设备类型判断钩子
 * 返回一个 ref，随窗口大小变化自动更新
 *
 * @returns {{
 *   isMobile: import('vue').Ref<boolean>,
 *   isDesktop: import('vue').Ref<boolean>
 * }} - 包含移动端和桌面端判断的响应式引用
 */
export const useResponsive = () => {
  const isMobile = ref(false);
  const isDesktop = ref(true);

  const updateDeviceType = () => {
    isMobile.value = isMobileDevice();
    isDesktop.value = isDesktopDevice();
  };

  onMounted(() => {
    updateDeviceType(); // 初始检查
    window.addEventListener("resize", updateDeviceType);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", updateDeviceType);
  });

  return {
    isMobile,
    isDesktop,
  };
};

/**
 * 创建基于特定断点的响应式判断钩子
 *
 * @param {number} breakpoint - 断点宽度
 * @returns {{
 *   isSmallerThanBreakpoint: import('vue').Ref<boolean>,
 *   isLargerThanBreakpoint: import('vue').Ref<boolean>
 * }} - 包含断点判断的响应式引用
 */
export const useBreakpoint = (breakpoint) => {
  const isSmallerThanBreakpoint = ref(false);
  const isLargerThanBreakpoint = ref(true);

  const updateBreakpoint = () => {
    isSmallerThanBreakpoint.value = isSmallerThan(breakpoint);
    isLargerThanBreakpoint.value = isLargerThan(breakpoint);
  };

  onMounted(() => {
    updateBreakpoint(); // 初始检查
    window.addEventListener("resize", updateBreakpoint);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", updateBreakpoint);
  });

  return {
    isSmallerThanBreakpoint,
    isLargerThanBreakpoint,
  };
};
