/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-23 15:30:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-23 15:37:46
 * @FilePath     : /src/utils/dateFormatter.js
 * @Description  : Date formatting utilities for internationalization
 * Copyright 2025 Bruce, All Rights Reserved.
 */

/**
 * 格式化更新日志日期显示，根据语言环境显示不同格式
 * @param {string} dateKey - 日期键，格式为 "YYYY-MM-DD"
 * @param {string} locale - 语言环境，如 "zh-TW" 或 "en"
 * @returns {string} 格式化后的日期字符串
 *
 * @example
 * formatChangelogDate("2025-07-17", "zh-TW") // "2025-07-17"
 * formatChangelogDate("2025-07-17", "en") // "Jul 17, 2025"
 */
export const formatChangelogDate = (dateKey, locale) => {
  // 从日期键提取年月日
  const [year, month, day] = dateKey.split("-");

  if (locale === "zh-TW") {
    // 中文显示：2025-07-17
    return `${year}-${month}-${day}`;
  } else {
    // 英文显示：Jul 17, 2025
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const monthName = monthNames[parseInt(month) - 1];
    return `${monthName} ${parseInt(day)}, ${year}`;
  }
};

/**
 * 生成更新日志锚点 ID
 * @param {string} dateKey - 日期键，格式为 "YYYY-MM-DD"
 * @returns {string} 锚点 ID
 *
 * @example
 * generateChangelogAnchorId("2025-07-17") // "release-2025-07-17"
 */
export const generateChangelogAnchorId = (dateKey) => {
  return `release-${dateKey}`;
};

/**
 * 从更新日志锚点 ID 提取日期键
 * @param {string} anchorId - 锚点 ID
 * @returns {string|null} 日期键或 null
 *
 * @example
 * extractDateKeyFromChangelogAnchor("release-2025-07-17") // "2025-07-17"
 * extractDateKeyFromChangelogAnchor("invalid-anchor") // null
 */
export const extractDateKeyFromChangelogAnchor = (anchorId) => {
  const match = anchorId.match(/^release-(\d{4}-\d{2}-\d{2})$/);
  return match ? match[1] : null;
};
