/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-22 10:30:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-22 13:46:18
 * @FilePath     : /src/utils/storage.js
 * @Description  : Storage utilities with fallback support
 * Copyright 2025 Bruce, All Rights Reserved.
 */

/**
 * 检查 localStorage 是否可用
 * @returns {boolean} 是否可用
 */
const isLocalStorageAvailable = () => {
  try {
    const test = "__localStorage_test__";
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch (e) {
    return false;
  }
};

/**
 * 统一的存储接口
 */
class Storage {
  constructor() {
    this.isLocalStorageSupported = isLocalStorageAvailable();

    // 如果 localStorage 不可用，在控制台给出提示
    if (!this.isLocalStorageSupported) {
      console.warn(
        "localStorage is not available. Settings will not persist across sessions."
      );
    }
  }

  /**
   * 获取存储的值
   * @param {string} key 键名
   * @param {any} defaultValue 默认值
   * @returns {any} 存储的值或默认值
   */
  getItem(key, defaultValue = null) {
    if (!this.isLocalStorageSupported) {
      return defaultValue;
    }

    try {
      const value = localStorage.getItem(key);
      if (value === null) {
        return defaultValue;
      }

      // 尝试解析 JSON，如果失败则返回原始字符串
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    } catch (error) {
      console.warn(`Failed to get item from storage: ${key}`, error);
      return defaultValue;
    }
  }

  /**
   * 设置存储的值
   * @param {string} key 键名
   * @param {any} value 值
   * @returns {boolean} 是否成功
   */
  setItem(key, value) {
    if (!this.isLocalStorageSupported) {
      return false;
    }

    try {
      const serializedValue =
        typeof value === "string" ? value : JSON.stringify(value);
      localStorage.setItem(key, serializedValue);
      return true;
    } catch (error) {
      console.warn(`Failed to set item in storage: ${key}`, error);
      return false;
    }
  }

  /**
   * 移除存储的值
   * @param {string} key 键名
   * @returns {boolean} 是否成功
   */
  removeItem(key) {
    if (!this.isLocalStorageSupported) {
      return false;
    }

    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn(`Failed to remove item from storage: ${key}`, error);
      return false;
    }
  }

  /**
   * 清空所有存储
   * @returns {boolean} 是否成功
   */
  clear() {
    if (!this.isLocalStorageSupported) {
      return false;
    }

    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.warn("Failed to clear storage", error);
      return false;
    }
  }

  /**
   * 检查键是否存在
   * @param {string} key 键名
   * @returns {boolean} 是否存在
   */
  hasItem(key) {
    return this.getItem(key) !== null;
  }

  /**
   * 获取所有键名
   * @returns {string[]} 键名数组
   */
  keys() {
    if (!this.isLocalStorageSupported) {
      return [];
    }

    try {
      return Object.keys(localStorage);
    } catch (error) {
      console.warn("Failed to get storage keys", error);
      return [];
    }
  }

  /**
   * 获取存储大小
   * @returns {number} 存储项数量
   */
  get length() {
    if (!this.isLocalStorageSupported) {
      return 0;
    }

    try {
      return localStorage.length;
    } catch (error) {
      console.warn("Failed to get storage length", error);
      return 0;
    }
  }

  /**
   * 检查是否支持持久化存储
   * @returns {boolean} 是否支持
   */
  get isPersistent() {
    return this.isLocalStorageSupported;
  }
}

// 创建单例实例
const storage = new Storage();

export default storage;

export { Storage };
