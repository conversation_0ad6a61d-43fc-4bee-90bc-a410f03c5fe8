/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-22 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-22 11:41:46
 * @FilePath     : /src/utils/language.js
 * @Description  : Language detection and management utilities
 * Copyright 2025 Bruce, All Rights Reserved.
 */

import storage from "./storage.js";

/**
 * 语言存储键名
 */
const LANGUAGE_STORAGE_KEY = "talka-language";

/**
 * 支持的语言列表
 */
export const SUPPORTED_LANGUAGES = ["en", "zh-TW"];

/**
 * 检测系统语言并返回对应的语言代码
 * 逻辑：如果第一语言是中文，则使用中文；否则使用英语
 * @returns {string} 语言代码 ('zh-TW' 或 'en')
 */
export const detectSystemLanguage = () => {
  const primaryLanguage = navigator.language;

  // 如果没有语言设置，返回默认英语
  if (!primaryLanguage) return "en";

  // 获取语言代码（去掉地区代码）
  const primaryLangCode = primaryLanguage.split("-")[0];

  return primaryLangCode === "zh" ? "zh-TW" : "en";
};

/**
 * 检查是否为支持的语言
 * @param {string} language 语言代码
 * @returns {boolean} 是否支持
 */
export const isSupportedLanguage = (language) => {
  return SUPPORTED_LANGUAGES.includes(language);
};

/**
 * 获取初始语言设置
 * 优先级：storage > 系统语言 > 默认英语
 * @returns {string} 语言代码
 */
export const getInitialLanguage = () => {
  const savedLanguage = storage.getItem(LANGUAGE_STORAGE_KEY);
  if (savedLanguage && isSupportedLanguage(savedLanguage)) {
    return savedLanguage;
  }

  // 如果没有保存的语言设置，则检测系统语言
  return detectSystemLanguage();
};

/**
 * 保存语言设置到storage
 * @param {string} language 语言代码
 * @returns {boolean} 是否保存成功
 */
export const saveLanguagePreference = (language) => {
  if (isSupportedLanguage(language)) {
    return storage.setItem(LANGUAGE_STORAGE_KEY, language);
  }
  return false;
};

/**
 * 检查存储是否支持持久化
 * @returns {boolean} 是否支持持久化
 */
export const isStoragePersistent = () => {
  return storage.isPersistent;
};
