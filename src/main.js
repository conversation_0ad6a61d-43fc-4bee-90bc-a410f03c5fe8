import { createApp } from "vue";
import "./style.css";
import "./styles/main.scss";
import App from "./App.vue";
import router from "./router";
import AOS from "aos";
import "aos/dist/aos.css";
// 导入i18n配置
import i18n from "./locales";

const app = createApp(App);

// 定义Vue全局变量，处理自定义元素
window._storylaneLoaded = window._storylaneLoaded || false;

// 使用路由和i18n插件
app.use(router);
app.use(i18n);

// 确保i18n在生产环境下正常工作
app.config.globalProperties.$t = i18n.global.t;
app.config.globalProperties.$i18n = i18n.global;

app.mount("#app");

AOS.init({
  duration: 800,
  easing: "ease-in-out",
  once: true,
});
