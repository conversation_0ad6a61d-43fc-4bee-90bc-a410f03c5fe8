:root {
  font-family: '<PERSON><PERSON><PERSON>', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light;
  color: #213547;
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.w-layout-blockcontainer {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
}

.w-container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.w-inline-block {
  max-width: 100%;
  display: inline-block;
}

.w-lightbox {
  text-decoration: none;
}

.w-video {
  width: 100%;
  position: relative;
  background: #000;
}

.w-embed {
  position: relative;
}

.embedly-embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

#app {
  width: 100%;
  margin: 0 auto;
}

.container {
  max-width: 1140px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
}

.w-container {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
}

.btn {
  padding: 9px 15px;
  border-radius: 3px;
  background-color: #3898ec;
  color: white;
  border: 0;
  line-height: inherit;
  text-decoration: none;
  cursor: pointer;
  display: inline-block;
}

.btn-secondary {
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
}

.btn-primary {
  background-color: #000;
  color: #fff;
}

.text-center {
  text-align: center;
}

.mb-24 {
  margin-bottom: 24px;
}

.mb-56 {
  margin-bottom: 56px;
}

.ml-24 {
  margin-left: 24px;
}

.w-nav {
  position: relative;
  background: #fff;
  z-index: 1000;
}

.w-nav-brand {
  position: relative;
  float: left;
  text-decoration: none;
  color: #333333;
}

.w-nav-menu {
  position: relative;
  float: right;
}

.nav-link {
  padding: 20px;
  color: #222;
  text-decoration: none;
  display: inline-block;
}

.text-span-15, .text-span-16 {
  color: #26c485;
  font-weight: 600;
}
