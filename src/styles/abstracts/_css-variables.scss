@use "responsive" as *;

// 基础 CSS 变量定义 (移动端优先)
:root {
  // 导航相关
  --nav-padding: 10px 20px;
  --nav-logo-width: 100px;
  --nav-link-padding: 14px 16px;
  --nav-link-margin: 10px 0;
  --nav-link-font-size: 16px;
  --menu-button-size: 40px;

  // 按钮相关
  --btn-padding: 10px 16px;
  --btn-font-size: 16px;
  --btn-border-radius: 6px;

  // 间距相关
  --spacing-xs: 8px;
  --spacing-sm: 10px;
  --spacing-md: 16px;
  --spacing-lg: 20px;
  --spacing-xl: 32px;

  // 移动菜单相关
  --mobile-menu-padding: 40px 20px 20px 20px;
}

// 平板端断点
@include respond-from("medium") {
  :root {
    --nav-padding: 10px 30px;
    --nav-logo-width: 110px;
    --nav-link-padding: 10px 16px;
    --nav-link-margin: 0 8px;
    --nav-link-font-size: 14px;
    --btn-padding: 8px 16px;
    --btn-font-size: 14px;
    --spacing-lg: 24px;
  }
}

// 桌面端断点
@include respond-from("large") {
  :root {
    --nav-padding: 10px 40px;
    --nav-logo-width: 120px;
    --nav-link-padding: 8px 16px;
    --nav-link-margin: 0 12px;
    --nav-link-font-size: 14px;
    --spacing-xl: 40px;
  }
}

// 宽屏桌面端断点
@include respond-from("xlarge") {
  :root {
    --nav-padding: 15px 40px;
    --nav-link-margin: 0 16px;
  }
}