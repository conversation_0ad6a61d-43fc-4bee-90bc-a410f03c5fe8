@use "sass:map"; // 添加 map 模块导入

// 响应式断点定义
$breakpoints: (
  'small': 480px,
  'medium': 768px,
  'large': 1024px,
  'xlarge': 1280px,
  'xxlarge': 1440px
);

// 大屏幕优先：使用 respond-to（小于或等于指定断点）
// 虽然 max-width 确实是在给小屏幕设置样式，但它是在"以大屏幕为默认设计"的思路下使用的，这就是为什么叫"大屏幕优先"
@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $value: map.get($breakpoints, $breakpoint);
    @media (max-width: $value) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// 移动端优先：使用 respond-from（大于或等于指定断点）
@mixin respond-from($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $value: map.get($breakpoints, $breakpoint);
    @media (min-width: $value) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// 处理两个断点之间的范围
@mixin respond-between($min-breakpoint, $max-breakpoint) {
  @if map.has-key($breakpoints, $min-breakpoint) and map.has-key($breakpoints, $max-breakpoint) {
    $min-value: map.get($breakpoints, $min-breakpoint);
    $max-value: map.get($breakpoints, $max-breakpoint);

    @media (min-width: $min-value) and (max-width: $max-value) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$min-breakpoint} or #{$max-breakpoint}";
  }
}

// PC端专用混入
@mixin desktop-only {
  @media (min-width: 769px) {
    @content;
  }
}

// 移动端专用混入
@mixin mobile-only {
  @media (max-width: 768px) {
    @content;
  }
}

// 平板端混入
@mixin tablet-only {
  @media (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}