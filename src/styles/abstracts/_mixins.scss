@use 'variables' as *;
@forward 'responsive';

// 响应式混入已移至 _responsive.scss，请直接使用 @include respond-to(), respond-from() 等

// 弹性布局
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// 容器居中
@mixin container {
  max-width: $container-max-width;
  margin-left: auto;
  margin-right: auto;
  padding-left: $spacing-sm;
  padding-right: $spacing-sm;
}
