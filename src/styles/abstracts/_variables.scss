// 颜色变量
$color-primary: #26c485;
$color-text: #213547;
$color-background: #ffffff;
$color-button: #3898ec;
$color-black: #000;
$color-white: #fff;

// 字体变量
$font-family-base: 'Poppins', system-ui, Avenir, Helvetica, Arial, sans-serif;
$font-weight-regular: 400;
$font-weight-semibold: 600;

// 容器宽度
$container-max-width: 1140px;
$layout-max-width: 1280px;

// 间距
$spacing-sm: 15px;
$spacing-md: 24px;
$spacing-lg: 56px;

// CSS变量 - 响应式设计
:root {
  // 导航相关
  --nav-padding: 10px 40px;
  --nav-logo-width: 120px;
  --nav-link-padding: 8px 16px;
  --nav-link-margin: 0 12px;
  --nav-link-font-size: 14px;
  --menu-button-size: 40px;

  // 按钮相关
  --btn-padding: 8px 16px;
  --btn-font-size: 14px;
  --btn-border-radius: 4px;

  // 间距相关
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 40px;

  // 移动菜单相关
  --mobile-menu-padding: 40px 20px 20px 20px;
}
