@use '@/styles/abstracts/variables' as *;
@use '@/styles/abstracts/mixins' as *;

.container {
  @include container;
}

.w-container {
  max-width: $layout-max-width;
  margin-left: auto;
  margin-right: auto;
}

.w-layout-blockcontainer {
  max-width: $layout-max-width;
  margin-left: auto;
  margin-right: auto;
}

.w-inline-block {
  max-width: 100%;
  display: inline-block;
}

.text-center {
  text-align: center;
}

// 边距工具类
.mb-24 {
  margin-bottom: $spacing-md;
}

.mb-56 {
  margin-bottom: $spacing-lg;
}

.ml-24 {
  margin-left: $spacing-md;
}
