<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-04-08 10:26:18
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-31 15:03:45
 * @FilePath     : /src/App.vue
 * @Description  : 主应用组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-04-08 10:26:18
-->
<script setup>
import Navbar from "./components/Navbar.vue";
import Footer from "./components/Footer.vue";
</script>

<template>
  <div id="app">
    <Navbar />
    <main class="main-content">
      <router-view />
    </main>
    <Footer />
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}
</style>
