<script setup></script>

<template>
  <section class="section-call-recording">
    <div class="w-layout-blockcontainer container-call-recording w-container">
      <div class="columns-call-recording w-row">
        <div class="text-call-rec w-col w-col-6">
          <div class="call-rec-title">{{ $t("callRecording.title") }}</div>
        </div>
        <div class="col-2-call-rec w-col w-col-6">
          <div class="call-rec-bullets">
            - {{ $t("callRecording.bullet1.prefix")
            }}<span class="text-span-15">{{
              $t("callRecording.bullet1.highlight")
            }}</span
            ><br />
            - {{ $t("callRecording.bullet2.prefix")
            }}<span class="text-span-16">{{
              $t("callRecording.bullet2.highlight")
            }}</span
            >{{ $t("callRecording.bullet2.suffix") }}
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.section-call-recording {
  padding: 60px 0;
  background-color: #f9fafb;
  background-image: url("https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65f03d5fd49d299e99693eb8_call-rec-bg.jpg");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.container-call-recording {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.columns-call-recording {
  display: flex;
  align-items: center;
  gap: 40px;
}

.text-call-rec {
  flex: 1;
}

.call-rec-title {
  font-size: 36px;
  line-height: 1.2;
  font-weight: 800;
  color: $color-white;
  margin-bottom: $spacing-md;
}

.col-2-call-rec {
  flex: 1;
}

.call-rec-bullets {
  font-size: 20px;
  line-height: 1.6;
  color: $color-white;
  flex-shrink: 1;
  min-width: 0; // 允许文本换行
}

.text-span-15,
.text-span-16 {
  color: $color-primary;
  font-weight: $font-weight-semibold;
}

// 使用项目标准断点
@include respond-to("xlarge") {
  .columns-call-recording {
    gap: 20px;
  }

  .text-call-rec {
    flex: 0 0 45%; // 固定左侧比例
  }

  .col-2-call-rec {
    flex: 1; // 右侧自适应
  }
}

@include respond-to("large") {
  .columns-call-recording {
    flex-direction: column;
    text-align: center;
  }

  .call-rec-title {
    margin-bottom: 20px;
  }
}

@include respond-to("medium") {
  .container-call-recording {
    padding: 0 20px;
  }

  .call-rec-title {
    font-size: 28px;
  }

  .call-rec-bullets {
    font-size: 18px;
  }
}
</style>
