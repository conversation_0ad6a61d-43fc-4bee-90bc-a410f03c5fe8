<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
import { useRoute } from "vue-router";
import LanguageSwitcher from "./LanguageSwitcher.vue";
import logoDark from "@/assets/logo_dark.png";

const isMenuOpen = ref(false);
const route = useRoute();

const toggleMenu = (event) => {
  if (event) {
    event.stopPropagation();
  }

  isMenuOpen.value = !isMenuOpen.value;

  // 当菜单打开时，禁止背景滚动
  if (isMenuOpen.value) {
    document.body.style.overflow = "hidden";
  } else {
    document.body.style.overflow = "";
  }
};

// 监听路由变化关闭菜单
const closeMenu = () => {
  isMenuOpen.value = false;
  document.body.style.overflow = "";
};

// 判断导航链接是否为激活状态
const isActive = (path) => {
  return route.path === path;
};

// 监听路由变化
onMounted(() => {
  // 监听路由变化，关闭移动端菜单
  const unwatch = watch(
    () => route.path,
    (newPath, oldPath) => {
      if (oldPath && newPath !== oldPath && isMenuOpen.value) {
        closeMenu();
      }
    }
  );

  // 监听窗口大小变化
  const handleResize = () => {
    if (window.innerWidth > 768 && isMenuOpen.value) {
      closeMenu();
    }
  };

  window.addEventListener("resize", handleResize);

  // 监听点击事件，点击菜单外部时关闭菜单
  const handleClickOutside = (event) => {
    const navElement = document.querySelector(".new-nav");
    const menuButton = document.querySelector(".menu-button");

    if (isMenuOpen.value && navElement && menuButton) {
      const isClickInsideNav = navElement.contains(event.target);
      const isClickOnMenuButton = menuButton.contains(event.target);

      // 只有当点击既不在导航菜单内，也不在菜单按钮上时，才关闭菜单
      if (!isClickInsideNav && !isClickOnMenuButton) {
        closeMenu();
      }
    }
  };

  document.addEventListener("click", handleClickOutside);

  onUnmounted(() => {
    // 清理：确保页面卸载时恢复滚动
    document.body.style.overflow = "";
    window.removeEventListener("resize", handleResize);
    document.removeEventListener("click", handleClickOutside);
    if (unwatch) unwatch();
  });
});
</script>

<template>
  <div class="w-nav">
    <div class="container nav-container w-container">
      <div class="wrapper nav-wrapper testar2">
        <router-link to="/" class="brand w-nav-brand" data-aos="fade-right">
          <img
            :src="logoDark"
            alt="Talka Logo"
            class="image-2"
            loading="eager"
          />
        </router-link>
        <!-- 移动端语言切换器 -->
        <LanguageSwitcher class="mobile-language-switcher" />
        <nav :class="['new-nav w-nav-menu', { 'nav-menu-open': isMenuOpen }]">
          <!-- 新增导航链接 -->
          <router-link
            to="/"
            :class="['nav-link', 'w-nav-link', { active: isActive('/') }]"
            @click="closeMenu"
            >{{ $t("navbar.home") }}</router-link
          >
          <router-link
            to="/help-center"
            :class="[
              'nav-link',
              'w-nav-link',
              { active: isActive('/help-center') },
            ]"
            @click="closeMenu"
            >{{ $t("navbar.helpCenter") }}</router-link
          >
          <router-link
            to="/info-center"
            :class="[
              'nav-link',
              'w-nav-link',
              { active: isActive('/info-center') },
            ]"
            @click="closeMenu"
            >{{ $t("navbar.infoCenter") }}</router-link
          >

          <LanguageSwitcher
            class="language-switcher-container desktop-language-switcher"
          />

          <a
            id="Signup"
            target="_blank"
            href="https://talka.tw/chatdemo"
            class="btn btn-primary ml-24"
          >
            {{ $t("navbar.signUpFree") }}
          </a>
        </nav>
        <div
          class="menu-button"
          @click="toggleMenu($event)"
          :class="{ 'is-active': isMenuOpen }"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            aria-hidden="true"
            role="img"
            class="icon h-6 w-6"
            width="24"
            height="24"
            viewBox="0 0 24 24"
          >
            <path
              fill="currentColor"
              d="M3 17h18a1 1 0 0 1 .117 1.993L21 19H3a1 1 0 0 1-.117-1.993L3 17h18zm0-6l18-.002a1 1 0 0 1 .117 1.993l-.117.007L3 13a1 1 0 0 1-.117-1.993L3 11l18-.002zm0-6h18a1 1 0 0 1 .117 1.993L21 7H3a1 1 0 0 1-.117-1.993L3 5h18z"
            ></path>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;
@use "@/styles/abstracts/responsive" as *;
@use "sass:color";

.w-nav {
  position: fixed;
  width: 100%;
  top: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  // 让 postcss-px-to-viewport 处理 padding
  padding: 10px 40px;
}

.wrapper {
  display: flex;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.brand {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.image-2 {
  width: 120px;
}

.new-nav {
  display: flex;
  align-items: center;
  padding: 0 24px 0 0;

  // 桌面端显示导航链接
  @include desktop-only {
    display: flex;
    flex-flow: row;
    width: auto;
    justify-content: flex-start;
  }

  // 移动端默认隐藏导航链接，通过 transform 控制显示
  @include mobile-only {
    display: flex;
    position: fixed;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    top: 0;
    left: 0;
    right: 0;
    background: $color-white;
    padding: 40px 20px 20px 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    // 强制隐藏在顶部，即使有其他样式冲突
    transform: translateY(-100%) !important;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1001;
    height: fit-content;
    // min-height: fit-content;
    // max-height: 100vh;
    overflow-y: auto;
    // border-bottom-left-radius: 16px;
    // border-bottom-right-radius: 16px;

    &.nav-menu-open {
      // 菜单打开时显示
      transform: translateY(0) !important;
    }
  }
}

.dropdown-2 {
  position: relative;
  display: inline-block;

  &:hover {
    .drop-down-list-menu,
    .w-dropdown-list {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
      display: block;
    }
  }
}

.dropdown-toggle-3 {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.text-mid {
  font-size: 14px;
  font-weight: 500;
  color: #222;
  font-family: $font-family-base;
  line-height: 20px;
}

.mr-8 {
  margin-right: 8px;
}

.drop-down-list-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: $color-white;
  min-width: 220px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.w-dropdown-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.w-dropdown-list {
  display: none;
  position: absolute;
  background-color: $color-white;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.dropdown-link,
.w-dropdown-link {
  display: block;
  // 让 postcss-px-to-viewport 处理尺寸
  padding: 12px 16px;
  color: #374151;
  text-decoration: none;
  transition: background-color 0.3s ease;
  font-family: $font-family-base;
  font-size: 14px;
  line-height: 20px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.nav-link,
.w-nav-link {
  vertical-align: top;
  color: #222;
  text-align: left;
  margin-left: auto;
  margin-right: 12px;
  padding: 8px 16px;
  text-decoration: none;
  display: inline-block;
  position: relative;
  font-family: $font-family-base;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    color: $color-primary;
    background-color: rgba($color-primary, 0.15);
  }

  // 选中状态样式
  &.active {
    color: $color-primary;
    background-color: rgba($color-primary, 0.3);
    font-weight: 600;
  }
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-family: $font-family-base;
  font-size: 14px;
  font-weight: $font-weight-semibold;
  line-height: 20px;
  text-decoration: none;
  transition: all 0.3s ease;
  text-align: center;
}

.btn-secondary {
  background-color: transparent;
  border: 1px solid #e5e7eb;
  color: #374151;

  &:hover {
    background-color: #f3f4f6;
  }
}

.btn-primary {
  background-color: $color-primary;
  color: $color-white;
  border: none;

  &:hover {
    background-color: color.adjust($color-primary, $lightness: -5%);
  }
}

.ml-24 {
  margin-left: 24px;
}

.menu-button {
  display: none;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  svg {
    color: #374151;
    transition: all 0.3s ease;
  }

  &.is-active {
    svg {
      transform: rotate(90deg);
      color: #2563eb;
    }
  }
}

.language-switcher-container {
  margin-left: auto;
}

.desktop-language-switcher {
  display: block;
}

.mobile-language-switcher {
  display: none;
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
}

@include mobile-only {
  // 移动端特定的导航链接样式

  .dropdown-2 {
    width: 100%;
    margin-bottom: 10px;

    &:hover .drop-down-list-menu {
      display: block;
    }
  }

  .dropdown-toggle-3 {
    width: 100%;
    justify-content: space-between;
  }

  .drop-down-list-menu {
    position: static;
    box-shadow: none;
    padding-left: 16px;
  }

  .nav-link {
    display: block;
    width: 90%;
    padding: 14px 16px;
    border-radius: 10px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;

    &.active {
      background-color: rgba(37, 99, 235, 0.12);
    }
  }

  .btn {
    display: block;
    width: 100%;
    text-align: center;
    margin-left: 0;
    margin-bottom: 10px;
  }

  .ml-24 {
    margin-left: 0;
    margin-top: 10px;
  }

  .menu-button {
    display: flex;
  }

  .language-switcher-container {
    margin-left: 0;
    margin-top: 10px;
    margin-bottom: 10px;
    align-self: flex-start;
  }

  .desktop-language-switcher {
    display: none;
  }

  .mobile-language-switcher {
    display: flex;
    margin-bottom: 0;
  }
}

@include respond-to("large") {
  a#Signup {
    display: none;
    margin: 0;
  }
}
</style>
