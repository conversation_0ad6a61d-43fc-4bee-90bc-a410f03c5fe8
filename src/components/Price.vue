<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-04-08 11:00:37
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 16:51:51
 * @FilePath     : /src/components/Price.vue
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-04-08 11:00:37
-->
<script setup>
import PriceHeader from "./price/PriceHeader.vue";
import StandardPriceTable from "./price/StandardPriceTable.vue";
import PackagePriceTable from "./price/PackagePriceTable.vue";
import OptimizedImage from "./OptimizedImage.vue";
import { useI18n } from "vue-i18n";

// 导入背景图片
import pcBackground from "@/assets/price/pc_background.png";
import mobileBackground from "@/assets/price/mobile_background.png";
</script>

<template>
  <section class="price-section">
    <div class="price-container">
      <!-- 背景图片优化 -->
      <OptimizedImage
        :src="pcBackground"
        alt=""
        class="price-background price-background--desktop"
        loading="lazy"
      />
      <OptimizedImage
        :src="mobileBackground"
        alt=""
        class="price-background price-background--mobile"
        loading="lazy"
      />

      <div class="price-content">
        <PriceHeader />
        <div class="price-tables-container">
          <StandardPriceTable />
          <PackagePriceTable />
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;
@use "sass:color";

.price-section {
  position: relative;
  padding: 80px 0 100px;
  overflow: hidden;
  background-color: #19283b;
  color: white;

  @media screen and (max-width: 767px) {
    padding: 60px 0 80px;
  }
}

.price-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 1;

  @media screen and (max-width: 767px) {
    padding: 0 20px;
  }
}

.price-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.9;
  object-fit: contain;
  object-position: left;

  &--desktop {
    display: block;

    @media screen and (max-width: 767px) {
      display: none;
    }
  }

  &--mobile {
    display: none;
    object-fit: cover;
    object-position: center;

    @media screen and (max-width: 767px) {
      display: block;
    }
  }
}

.price-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  z-index: 1;
}

.price-tables-container {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  width: 100%;

  @media screen and (max-width: 991px) {
    flex-direction: column;
  }
}
</style>
