<template>
  <div class="optimized-image">
    <img
      ref="imgRef"
      :src="placeholder"
      :data-src="src"
      :data-srcset="srcset"
      :sizes="sizes"
      :alt="alt"
      :loading="loading"
      :class="[
        'optimized-image__img',
        { 'optimized-image__img--loaded': isLoaded },
      ]"
      @load="handleLoad"
      @error="handleError"
    />

    <!-- 加载状态 -->
    <div
      v-if="showPlaceholder && !isLoaded"
      class="optimized-image__placeholder"
    >
      <div class="optimized-image__skeleton"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  srcset: {
    type: String,
    default: "",
  },
  sizes: {
    type: String,
    default: "100vw",
  },
  alt: {
    type: String,
    default: "",
  },
  loading: {
    type: String,
    default: "lazy", // 'lazy' | 'eager'
    validator: (value) => ["lazy", "eager"].includes(value),
  },
  placeholder: {
    type: String,
    default:
      "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y0ZjRmNCIvPjwvc3ZnPg==",
  },
  showPlaceholder: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["load", "error"]);

const imgRef = ref(null);
const isLoaded = ref(false);
const hasError = ref(false);
const observer = ref(null);

const handleLoad = () => {
  isLoaded.value = true;
  emit("load");
};

const handleError = (event) => {
  hasError.value = true;
  console.warn("OptimizedImage failed to load:", props.src, event);
  // 即使出错也要显示图片（使用浏览器默认的坏图图标）
  isLoaded.value = true;
  emit("error", event);
};

// 懒加载实现
const setupLazyLoading = () => {
  if (!imgRef.value) return;

  // 如果是eager加载，立即设置图片源
  if (props.loading === "eager") {
    const img = imgRef.value;
    // 清除data-src，直接设置src
    img.removeAttribute("data-src");
    img.removeAttribute("data-srcset");
    img.src = props.src;
    if (props.srcset) {
      img.srcset = props.srcset;
    }
    return;
  }

  // 懒加载逻辑
  if (props.loading === "lazy") {
    observer.value = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target;
            const src = img.dataset.src;
            const srcset = img.dataset.srcset;

            if (src) img.src = src;
            if (srcset) img.srcset = srcset;

            observer.value.unobserve(img);
          }
        });
      },
      {
        rootMargin: "50px 0px", // 提前50px开始加载
        threshold: 0.1,
      }
    );

    observer.value.observe(imgRef.value);
  }
};

onMounted(() => {
  setupLazyLoading();
});

onUnmounted(() => {
  if (observer.value) {
    observer.value.disconnect();
  }
});
</script>

<style scoped>
.optimized-image {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
}

.optimized-image__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  opacity: 0.1; /* 稍微可见，避免完全透明 */
}

.optimized-image__img--loaded {
  opacity: 1;
}

.optimized-image__placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f4f4f4;
  z-index: 1;
}

.optimized-image__skeleton {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f4f4f4 25%, #e0e0e0 50%, #f4f4f4 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .optimized-image__img {
    object-fit: cover;
  }
}
</style>
