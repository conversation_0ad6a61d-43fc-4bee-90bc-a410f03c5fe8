<script setup></script>

<template>
  <section class="section-global-team">
    <div class="w-layout-blockcontainer container-global-team w-container">
      <div class="columns-global-t w-row">
        <div class="text-globalt w-col w-col-6 w-col-small-small-stack">
          <div class="global-t-title">
            {{ $t("globalTeam.title.prefix") }}
            <span class="text-span-14">{{
              $t("globalTeam.title.highlight")
            }}</span>
          </div>
          <div class="global-t-text">{{ $t("globalTeam.description") }}</div>
        </div>
        <div class="image-globalt w-col w-col-6 w-col-small-small-stack">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cf9fcc1437ecad2d7967a1_global_2.png"
            loading="lazy"
            sizes="(max-width: 479px) 100vw, (max-width: 767px) 87vw, 40vw"
            srcset="
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cf9fcc1437ecad2d7967a1_global_2-p-500.png   500w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cf9fcc1437ecad2d7967a1_global_2-p-800.png   800w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cf9fcc1437ecad2d7967a1_global_2-p-1080.png 1080w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cf9fcc1437ecad2d7967a1_global_2.png        1272w
            "
            alt=""
            class="image-11"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;
@use "sass:color";

.section-global-team {
  background-color: #19283b;
  padding: 60px 120px;

  @media screen and (max-width: 991px) {
    padding: 60px 40px;
  }

  @include mobile-only {
    padding: 40px 20px;
  }
}

.container-global-team {
  align-items: center;
  max-width: 1280px;
  display: flex;
  margin: 0 auto;
}

.columns-global-t {
  width: 100%;
  display: flex;
  align-items: center;

  @include mobile-only {
    flex-direction: column;
  }
}

.text-globalt {
  flex-direction: column;
  justify-content: center;
  display: flex;
  flex: 1;

  @include mobile-only {
    text-align: center;
    margin-bottom: 30px;
  }
}

.global-t-title {
  color: $color-white;
  font-size: 38px;
  font-weight: 600;
  line-height: 120%;
  margin-bottom: 16px;

  @include mobile-only {
    max-width: 90%;
    font-size: 32px;
    margin: 0 auto 16px;
  }
}

.text-span-14 {
  color: #2563eb;
}

.global-t-text {
  color: #d9d9d9;
  font-size: 24px;
  font-weight: 400;
  line-height: 1.4;
}

.image-globalt {
  justify-content: center;
  align-self: center;
  display: flex;
  overflow: visible;
  flex: 1;

  @include mobile-only {
    justify-content: center;
    align-self: center;
    display: flex;
  }
}

.image-11 {
  max-width: 100%;
  height: auto;

  @include mobile-only {
    max-width: 100%;
  }
}
</style>
