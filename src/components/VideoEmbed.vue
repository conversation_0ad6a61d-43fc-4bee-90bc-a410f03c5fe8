<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-24 16:18:26
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 16:33:05
 * @FilePath     : /src/components/VideoEmbed.vue
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-24 16:18:26
-->
<template>
  <div class="video-embed">
    <iframe
      v-if="videoData"
      class="embedly-embed"
      :src="videoData.src"
      :width="videoData.width"
      :height="videoData.height"
      scrolling="no"
      title="Vimeo embed"
      frameborder="0"
      allow="autoplay; fullscreen; encrypted-media; picture-in-picture"
      allowfullscreen="true"
    ></iframe>
  </div>
</template>

<script>
export default {
  name: "VideoEmbed",
  data() {
    return {
      videoData: {
        width: 940,
        height: 529,
        src: "//cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fplayer.vimeo.com%2Fvideo%2F948295359%3Fapp_id%3D122963&dntp=1&display_name=Vimeo&url=https%3A%2F%2Fplayer.vimeo.com%2Fvideo%2F948295359&image=https%3A%2F%2Fi.vimeocdn.com%2Fvideo%2F1855565887-f4990bb1bcd13ff0a65c74c2d986ed2901efb82ae2ac8cc890059c95ed1d4e1e-d_1280&key=96f1f04c5f4143bcb0f2e68c87d65feb&type=text%2Fhtml&schema=vimeo",
      },
    };
  },
};
</script>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.video-embed {
  width: 100%;
  max-width: 940px;
  margin: 0 auto;
}
</style>
