<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-21 15:30:05
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 15:55:02
 * @FilePath     : /src/components/CallToAction.vue
 * @Description
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-03-21 15:30:05
-->
<template>
  <section id="cto" class="section cto">
    <div class="wrapper">
      <div class="container center w-container">
        <div class="title _960">
          <p class="huge-paragraph text-center text-light mb-40 cto-mobile">
            {{ $t("callToAction.title") }} <br />{{
              $t("callToAction.subtitle")
            }}
          </p>
          <a
            target="_blank"
            href="https://talka.tw/chatdemo"
            class="button btn-white w-button"
            >{{ $t("callToAction.startNow") }}</a
          >
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// 组件逻辑可以在这里添加
</script>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.section.cto {
  background-color: $color-primary;
  padding: 80px 0;

  @include mobile-only {
    padding: 60px 0;
  }
}

.wrapper {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.huge-paragraph {
  font-size: 36px;
  line-height: 1.3;
  font-weight: 600;
  color: $color-white;

  @include mobile-only {
    font-size: 24px;
  }
}

.text-center {
  text-align: center;
}

.text-light {
  color: $color-white;
}

.mb-40 {
  margin-bottom: 40px;

  @include mobile-only {
    margin-bottom: 30px;
  }
}

.button.btn-white {
  display: inline-block;
  padding: 12px 24px;
  background-color: $color-white;
  color: $color-primary;
  font-weight: 600;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
}
</style>
