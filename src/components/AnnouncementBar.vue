<script setup>
import { ref } from "vue";

const isVisible = ref(true);

const closeAnnouncement = () => {
  isVisible.value = false;
};
</script>

<template>
  <div v-if="isVisible" class="anouncement-bar" data-aos="fade-down">
    <!-- <div class="anouncement-content">
      <div class="notification">{{ $t('notification') }} <a
          href="https://www.cbinsights.com/research/artificial-intelligence-top-startups-2023/" class="link-top">CB
          Insights</a></div>
      <a @click="closeAnnouncement" href="#" class="top-close">
        <img
          src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/631b2c0597042d6aecc00a7e_close-white-icon.svg"
          loading="lazy" :alt="$t('closeIcon')" class="top-close-icon" />
      </a>
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.anouncement-bar {
  background-color: $color-black;
  color: $color-white;
  padding: 10px 0;
  transition: all 0.3s ease;
  transform-origin: top;
  animation: slideDown 0.5s ease-out;

  &.closing {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.anouncement-content {
  max-width: 940px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
}

.notification {
  font-size: 14px;
  opacity: 0.9;
}

.link-top {
  color: $color-white;
  text-decoration: underline;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

.top-close {
  display: flex;
  align-items: center;
  cursor: pointer;
  text-decoration: none;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);

    .top-close-icon {
      transform: rotate(90deg);
    }
  }
}

.top-close-icon {
  width: 12px;
  height: 12px;
}

@include mobile-only {
  .notification {
    font-size: 12px;
  }

  .anouncement-content {
    padding: 0 10px;
  }
}
</style>
