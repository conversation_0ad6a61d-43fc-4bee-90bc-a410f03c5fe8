<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-17 20:18:22
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-04-09 13:58:38
 * @FilePath     : /src/components/LanguageSwitcher.vue
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-03-17 20:18:22
-->
<script setup>
import { useI18n } from "vue-i18n";
import { saveLanguagePreference, isStoragePersistent } from "@/utils/language";

const { locale, t } = useI18n();

const switchLanguage = (lang) => {
  locale.value = lang;
  const saved = saveLanguagePreference(lang);

  // 如果保存失败且不支持持久化存储，给用户提示
  if (!saved && !isStoragePersistent()) {
    console.warn(
      "Language preference could not be saved. Settings will not persist across sessions."
    );
  }
};
</script>

<template>
  <div class="language-switcher">
    <button
      @click="switchLanguage('en')"
      :class="{ active: locale === 'en' }"
      class="lang-btn"
    >
      {{ t("languageSwitcher.english") }}
    </button>
    <span class="divider">|</span>
    <button
      @click="switchLanguage('zh-TW')"
      :class="{ active: locale === 'zh-TW' }"
      class="lang-btn"
    >
      {{ t("languageSwitcher.chineseTraditional") }}
    </button>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.language-switcher {
  display: flex;
  align-items: center;
  margin-left: 20px;

  @media (max-width: 767px) {
    margin-left: 10px;
  }
}

.lang-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px 8px;
  font-size: 14px;
  color: #4b5563;
  transition: color 0.3s;

  &:hover {
    color: #000000;
  }

  &.active {
    font-weight: bold;
    color: #000000;
  }

  @media (max-width: 767px) {
    padding: 3px 5px;
    font-size: 12px;
  }
}

.divider {
  color: #d1d5db;
  margin: 0 2px;
}
</style>
