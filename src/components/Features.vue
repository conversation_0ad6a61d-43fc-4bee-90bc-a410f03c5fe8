<script setup>
import { onMounted } from "vue";

onMounted(() => {
  // 动态加载storylane.js脚本
  const script = document.createElement("script");
  script.src = "https://js.storylane.io/js/v1/storylane.js";
  script.async = true;
  document.head.appendChild(script);
});
</script>

<template>
  <section id="about" class="section features">
    <div class="wrapper">
      <div class="container mobile-full w-container">
        <!-- <div class="w-layout-blockcontainer demo-container midle w-container">
          <h3 class="title-feature-main title-feature center">{{ $t('features.demo.title') }}</h3>
          <div class="div-block-4">
            <div class="short w-embed w-iframe">
              <div>
                <div class="sl-embed"
                  style="position:relative;padding-bottom:calc(62.50% + 27px);width:100%;height:0;transform:scale(1)">
                  <iframe class="sl-demo" src="https://app.storylane.io/demo/4amnkqwj9eld" name="sl-embed"
                    allow="fullscreen; camera; microphone"
                    style="position:absolute;top:0;left:0;width:100%;height:100%;border:1px solid rgba(63,95,172,0.35);box-shadow: 0px 0px 18px rgba(26, 19, 72, 0.15);border-radius:10px;box-sizing:border-box;"></iframe>
                </div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
      <div class="for-content mb-120 mobile-mb-30 w-row">
        <div
          class="left w-col w-col-6 w-col-stack w-col-small-small-stack w-col-tiny-tiny-stack"
        >
          <div class="feature-text">
            <!-- <h4 class="tag-persona">{{ $t('features.salesReps.tag') }}</h4> -->
            <h3 class="title-feature-main title-feature">
              {{ $t("features.salesReps.title") }}
            </h3>
            <img
              src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6f56bac9ff4a82231bae_divider%20-%20red.svg"
              loading="lazy"
              alt=""
              class="divider-color"
            />
            <p class="paragraph mb-24 mobile-right-align text-middark">
              {{ $t("features.salesReps.description") }}
            </p>
            <!-- <a id="Talka-For-Sales-Reps"
								href="/for-sales-reps" class="btn btn-secondary line secondary w-button">
								{{ $t('features.salesReps.cta') }}
							</a> -->
          </div>
        </div>
        <div
          class="right center features w-col w-col-6 w-col-stack w-col-small-small-stack w-col-tiny-tiny-stack"
        >
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6dbe692f356ae3dd4238_meeting%20score_1x2.png"
            loading="lazy"
            sizes="(max-width: 479px) 92vw, (max-width: 767px) 85vw, (max-width: 991px) 83vw, 38vw"
            srcset="
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6dbe692f356ae3dd4238_meeting%20score_1x2-p-500.png   500w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6dbe692f356ae3dd4238_meeting%20score_1x2-p-800.png   800w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6dbe692f356ae3dd4238_meeting%20score_1x2-p-1080.png 1080w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6dbe692f356ae3dd4238_meeting%20score_1x2.png        1232w
            "
            alt=""
            class="image-feature right"
          />
        </div>
      </div>
      <div class="for-content mb-120 mobile-mb-30 reverse w-row">
        <div class="left center mobile-nomargin w-col w-col-6 w-col-stack">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba705b1c6b841f719fe71c_scoring-teamx2.png"
            loading="lazy"
            sizes="(max-width: 479px) 92vw, (max-width: 767px) 85vw, (max-width: 991px) 83vw, 100vw"
            srcset="
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba705b1c6b841f719fe71c_scoring-teamx2-p-500.png   500w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba705b1c6b841f719fe71c_scoring-teamx2-p-800.png   800w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba705b1c6b841f719fe71c_scoring-teamx2-p-1080.png 1080w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba705b1c6b841f719fe71c_scoring-teamx2.png        1416w
            "
            alt=""
            class="image-feature right"
          />
        </div>
        <div class="right pl-32 w-col w-col-6 w-col-stack">
          <div class="feature-text">
            <!-- <h4 class="tag-persona">{{ $t('features.salesManagers.tag') }}</h4> -->
            <h3 class="title-feature-main title-feature">
              {{ $t("features.salesManagers.title") }}
            </h3>
            <img
              src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6ffa43e286168bf321eb_divider%20-%20blue.svg"
              loading="lazy"
              alt=""
              class="divider-color"
            />
            <p class="paragraph mb-24 mobile-right-align text-middark">
              {{ $t("features.salesManagers.description") }}
            </p>
            <!-- <a
								href="/for-sales-managers" class="btn btn-secondary line secondary w-button">
								{{ $t('features.salesManagers.cta') }}
							</a> -->
          </div>
        </div>
      </div>
      <div class="for-content mb-120 mobile-mb-30 w-row">
        <div
          class="left w-col w-col-6 w-col-stack w-col-small-small-stack w-col-tiny-tiny-stack"
        >
          <div class="feature-text">
            <!-- <h4 class="tag-persona">{{ $t('features.headOfSales.tag') }}</h4> -->
            <h3 class="title-feature-main title-feature">
              {{ $t("features.headOfSales.title") }}
            </h3>
            <img
              src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6ffa0787739b3468abc0_divider%20-%20yellow.svg"
              loading="lazy"
              alt=""
              class="divider-color"
            />
            <p class="paragraph mb-24 mobile-right-align text-middark">
              {{ $t("features.headOfSales.description") }}
            </p>
            <!-- <a href="/head-of-sales"
								class="btn btn-secondary line secondary w-button">{{ $t('features.headOfSales.cta') }}
							</a> -->
          </div>
        </div>
        <div
          class="right center features w-col w-col-6 w-col-stack w-col-small-small-stack w-col-tiny-tiny-stack"
        >
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba7490d695a4cdca4019eb_forecast_x2.png"
            loading="lazy"
            sizes="(max-width: 479px) 92vw, (max-width: 767px) 85vw, (max-width: 991px) 83vw, 38vw"
            srcset="
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba7490d695a4cdca4019eb_forecast_x2-p-500.png   500w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba7490d695a4cdca4019eb_forecast_x2-p-800.png   800w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba7490d695a4cdca4019eb_forecast_x2-p-1080.png 1080w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba7490d695a4cdca4019eb_forecast_x2.png        1380w
            "
            alt=""
            class="image-feature right"
          />
        </div>
      </div>
      <div class="for-content mb-120 mobile-mb-30 reverse w-row">
        <div class="left center mobile-nomargin w-col w-col-6 w-col-stack">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba74d2c4fac0dae6b9b5d1_analysis_x2.png"
            loading="lazy"
            sizes="(max-width: 479px) 92vw, (max-width: 767px) 85vw, (max-width: 991px) 83vw, 100vw"
            srcset="
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba74d2c4fac0dae6b9b5d1_analysis_x2-p-500.png   500w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba74d2c4fac0dae6b9b5d1_analysis_x2-p-800.png   800w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba74d2c4fac0dae6b9b5d1_analysis_x2-p-1080.png 1080w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba74d2c4fac0dae6b9b5d1_analysis_x2.png        1338w
            "
            alt=""
            class="image-feature right"
          />
        </div>
        <div class="right pl-32 w-col w-col-6 w-col-stack">
          <div class="feature-text">
            <!-- <h4 class="tag-persona">{{ $t('features.dealAnalysis.tag') }}</h4> -->
            <h3 class="title-feature-main title-feature">
              {{ $t("features.dealAnalysis.title") }}
            </h3>
            <img
              src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6f56bac9ff4a82231bae_divider%20-%20red.svg"
              loading="lazy"
              alt=""
              class="divider-color"
            />
            <p class="paragraph mb-24 mobile-right-align text-middark">
              {{ $t("features.dealAnalysis.description") }}
            </p>
            <!-- <a href="/for-sales-reps"
								class="btn btn-secondary line secondary w-button">
								{{ $t('features.dealAnalysis.cta') }}
							</a> -->
          </div>
        </div>
      </div>
      <div class="for-content mb-120 mobile-mb-30 w-row">
        <div
          class="left w-col w-col-6 w-col-stack w-col-small-small-stack w-col-tiny-tiny-stack"
        >
          <div class="feature-text">
            <!-- <h4 class="tag-persona">{{ $t('features.revenueMaximization.tag') }}</h4> -->
            <h3 class="title-feature-main title-feature">
              {{ $t("features.revenueMaximization.title") }}
            </h3>
            <img
              src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6ffa43e286168bf321eb_divider%20-%20blue.svg"
              loading="lazy"
              alt=""
              class="divider-color"
            />
            <p class="paragraph mb-24 mobile-right-align text-middark">
              {{ $t("features.revenueMaximization.description") }}
            </p>
            <!-- <a href="/for-sales-managers"
								class="btn btn-secondary line secondary w-button">
								{{ $t('features.revenueMaximization.cta') }}
							</a> -->
          </div>
        </div>
        <div
          class="right center features w-col w-col-6 w-col-stack w-col-small-small-stack w-col-tiny-tiny-stack"
        >
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cfa2b35e6de6df0b043f44_revenue.png"
            loading="lazy"
            width="689"
            sizes="(max-width: 479px) 92vw, (max-width: 767px) 85vw, (max-width: 991px) 83vw, 38vw"
            alt=""
            srcset="
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cfa2b35e6de6df0b043f44_revenue-p-500.png   500w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cfa2b35e6de6df0b043f44_revenue-p-800.png   800w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cfa2b35e6de6df0b043f44_revenue-p-1080.png 1080w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65cfa2b35e6de6df0b043f44_revenue.png        1378w
            "
            class="image-feature right"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;
@use "sass:color";

.features {
  padding: 60px 0;
  background-color: #f9fafb;
}

.wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.container {
  width: 100%;
}

.for-content {
  margin-bottom: 120px;
  display: flex;
  align-items: center;

  &.reverse {
    flex-direction: row;
  }

  &.mb-120 {
    margin-bottom: 120px;
  }

  &.mobile-mb-30 {
    @media screen and (max-width: 991px) {
      margin-bottom: 30px;
    }
  }
}

.left,
.right {
  width: 50%;
  padding: 0 15px;
  display: flex;
  justify-content: center;
  align-items: center;

  &.center {
    text-align: center;
  }

  &.pl-32 {
    padding-left: 32px;
  }

  &.mobile-nomargin {
    @media screen and (max-width: 991px) {
      margin-bottom: 0;
    }
  }
}

.feature-text {
  max-width: 480px;
}

.tag-persona {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.title-feature {
  font-size: 36px;
  line-height: 1.2;
  font-weight: 800;
  color: #111827;
  margin-bottom: 24px;

  &.center {
    text-align: center;
    margin: 0 auto 40px;
  }
}

.divider-color {
  margin-bottom: 24px;
}

.text-middark {
  color: #4b5563;
  font-size: 18px;
  line-height: 1.6;
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;

  &-secondary {
    background-color: $color-white;
    border: 1px solid #e5e7eb;
    color: #374151;

    &:hover {
      background-color: #f3f4f6;
      transform: translateY(-2px);
    }
  }
}

.image-feature {
  max-width: 100%;
  width: auto;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba($color-black, 0.1);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-4px);
  }

  &.right {
    display: block;
  }
}

.demo-container {
  margin-bottom: 80px;
}

.div-block-4 {
  max-width: 1280px;
  margin: 0 auto;
}

.short {
  width: 100%;
  display: block;
}

.w-embed {
  position: relative;
  display: block;
  width: 100%;
}

.w-iframe {
  display: block;
  width: 100%;
}

.sl-embed {
  width: 100%;
}

.paragraph {
  &.mb-24 {
    margin-bottom: 24px;
  }

  &.mobile-right-align {
    @include mobile-only {
      text-align: left;
    }
  }
}

@media screen and (max-width: 991px) {
  .for-content {
    flex-direction: column;
    margin-bottom: 60px;

    &.reverse {
      flex-direction: column;
    }
  }

  .left,
  .right {
    width: 100%;
    padding: 0;
    margin-bottom: 40px;
  }

  .feature-text {
    max-width: 100%;
  }
}

@include mobile-only {
  .wrapper {
    padding: 0 20px;
  }

  .title-feature {
    font-size: 28px;
  }

  .demo-container {
    margin-bottom: 60px;
  }

  .div-block-4 {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@include respond-to("small") {
  .features {
    padding: 40px 0;
  }

  .title-feature {
    font-size: 24px;
  }
}
</style>
