<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-15 16:25:15
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 16:15:57
 * @FilePath     : /src/components/Footer.vue
 * @Description  :
 * Copyright 2025 <PERSON>, All Rights Reserved.
 * 2025-03-15 16:25:15
-->
<template>
  <footer id="footer" class="footer">
    <div class="wrapper">
      <div class="container w-container">
        <div class="footer-flex-container">
          <a href="#" class="footer-logo-link">
            <img
              :src="logoLight"
              alt="Talka Logo"
              class="footer-image"
              loading="lazy"
            />
          </a>
          <!-- <div class="footer-links">
            <h2 class="footer-heading">{{ t("footer.solutions.title") }}</h2>
            <ul role="list" class="w-list-unstyled">
              <li>
                <a class="footer-link">{{ t("footer.solutions.salesReps") }}</a>
              </li>
              <li>
                <a class="footer-link">{{
                  t("footer.solutions.salesManagers")
                }}</a>
              </li>
              <li>
                <a class="footer-link">{{
                  t("footer.solutions.headOfSales")
                }}</a>
              </li>
            </ul>
          </div> -->
          <!-- <div class="footer-links">
            <h2 class="footer-heading">{{ t("footer.company.title") }}</h2>
            <ul role="list" class="w-list-unstyled">
              <li>
                <a href="/about-us" class="footer-link">{{
                  t("footer.company.aboutUs")
                }}</a>
              </li>
              <li>
                <a href="/blog" class="footer-link">{{
                  t("footer.company.blog")
                }}</a>
              </li>
              <li>
                <a href="/pricing" class="footer-link">{{
                  t("footer.company.pricing")
                }}</a>
              </li>
            </ul>
          </div> -->
          <div class="footer-links">
            <h2 class="footer-heading">{{ t("footer.contact.title") }}</h2>
            <ul role="list" class="w-list-unstyled">
              <li>
                <a href="mailto:<EMAIL>" class="footer-link">
                  {{ t("footer.contact.email") }}
                  <EMAIL>
                </a>
              </li>
              <li>
                <a
                  href="https://t.me/TalkaCS"
                  class="footer-link"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Telegram: @TalkaCS
                </a>
              </li>
              <!-- <li>
                <a class="footer-link">{{ t("footer.company.aboutUs") }}</a>
              </li> -->
              <!-- <li>
                <a
                  href="https://insights.talka.ai/signup"
                  class="footer-link"
                  >{{ t("footer.contact.signup") }}</a
                >
              </li> -->
            </ul>
          </div>
          <div class="footer-links">
            <h2 class="footer-heading">{{ t("footer.legal.title") }}</h2>
            <ul role="list" class="w-list-unstyled">
              <li>
                <a class="footer-link">{{ t("footer.legal.privacyPolicy") }}</a>
              </li>
              <li>
                <a class="footer-link">{{
                  t("footer.legal.termsOfService")
                }}</a>
              </li>
            </ul>
          </div>
        </div>
        <div class="footer-legal">
          <div class="footer-legal-row">
            <div class="footer-legal-col _1">
              <div class="paragraph-footer">{{ t("footer.copyright") }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import OptimizedImage from "./OptimizedImage.vue";
import logoLight from "@/assets/logo_light.png";

const { t } = useI18n();
</script>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.footer {
  color: $color-white;
  padding: 60px 0 30px;

  @include mobile-only {
    padding: 40px 0 20px;
  }
}

.wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;

  @include mobile-only {
    padding: 0 20px;
  }
}

.footer-flex-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 40px;

  @include mobile-only {
    flex-direction: column;
    gap: 30px;
  }
}

.footer-logo-link {
  margin-right: 40px;
  margin-bottom: 20px;

  @include mobile-only {
    margin-bottom: 0;
  }
}

.footer-image {
  width: 140px;
  height: auto;
  max-width: 140px;
}

.footer-links {
  margin-bottom: 20px;
  min-width: 120px;
}

.footer-heading {
  color: $color-white;
  font-size: 16px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 16px;
}

.w-list-unstyled {
  padding-left: 0;
  list-style: none;
}

.footer-link {
  color: rgba($color-white, 0.7);
  text-decoration: none;
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  transition: color 0.3s ease;

  &:hover {
    color: $color-white;
    text-decoration: underline;
  }
}

.footer-legal {
  padding-top: 20px;
  border-top: 1px solid rgba($color-white, 0.1);
}

.footer-legal-row {
  display: flex;
  justify-content: center;
  align-items: center;

  @include mobile-only {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
}

.paragraph-footer {
  color: rgba($color-white, 0.5);
  font-size: 12px;
}

/* 重写OptimizedImage的默认样式 */
.footer-image :deep(.optimized-image__img) {
  width: 140px;
  height: auto;
  max-width: 140px;
  object-fit: contain;
}

.footer-image :deep(.optimized-image) {
  width: 140px;
  height: auto;
  max-width: 140px;
  display: inline-block;
}
</style>
