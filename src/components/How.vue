<script setup>
// 导入i18n
import { useI18n } from "vue-i18n";
import OptimizedImage from "./OptimizedImage.vue";

// 导入图片
import howImg1SrcsetPc500 from "@/assets/how_img1_srcset_pc500.png";
import howImg1SrcsetPc800 from "@/assets/how_img1_srcset_pc800.png";
import howImg1SrcsetPc1080 from "@/assets/how_img1_srcset_pc1080.png";
import howImg1SrcsetPc1600 from "@/assets/how_img1_srcset_pc1600.png";
import howImg1SrcsetPc2000 from "@/assets/how_img1_srcset_pc2000.png";
import howImg1SrcMobile from "@/assets/how_img1_src_mobile.png";
import howImg1SrcsetMobile from "@/assets/how_img1_srcset_mobile.png";
import telegramImg from "@/assets/how/telegram.png";
import websiteImg from "@/assets/how/website.png";

const { t } = useI18n();
</script>

<template>
  <section class="how">
    <img
      src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba313abfb9a2a5c052ae85_wave.svg"
      loading="lazy"
      height="117"
      alt=""
      class="image-7"
    />
    <div
      class="w-layout-blockcontainer container center center-mobile mt-56 w-container"
    >
      <h2 class="h2 text-center small">
        {{ t("how.mainTitle") }}<br />
        <span class="smaller">{{ t("how.subtitle") }}</span
        ><br />
      </h2>
      <OptimizedImage
        :src="howImg1SrcsetPc1600"
        :srcset="`
          ${howImg1SrcsetPc500}   500w,
          ${howImg1SrcsetPc800}   800w,
          ${howImg1SrcsetPc1080} 1080w,
          ${howImg1SrcsetPc1600} 1600w,
          ${howImg1SrcsetPc2000} 2000w
        `"
        alt="How it works - Desktop view"
        sizes="(max-width: 767px) 100vw, (max-width: 1200px) 60vw, 720px"
        class="image-8 desktop"
      />
      <OptimizedImage
        :src="howImg1SrcMobile"
        :srcset="`
          ${howImg1SrcsetMobile} 500w,
          ${howImg1SrcMobile}    662w
        `"
        alt="How it works - Mobile view"
        sizes="(max-width: 662px) 100vw, (max-width: 767px) 662px, 100vw"
        class="image-8 mobile"
      />
      <div class="columns col-80 w-row">
        <div class="card box-shadow w-col w-col-4 w-col-stack">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba574323964e85204e8666_icon-01.svg"
            loading="lazy"
            alt=""
            class="image-3 mb-32"
          />
          <div class="title">
            <h3 class="h3 mb-16 t-20">
              {{ t("how.cards[0].title") }}
            </h3>
            <p class="paragraph t-18">
              {{ t("how.cards[0].description") }}
            </p>
          </div>
        </div>
        <div class="card box-shadow w-col w-col-4 w-col-stack">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba5743f989fa220d934c92_icon-02.svg"
            loading="lazy"
            alt=""
            class="image-3 mb-32"
          />
          <div class="title">
            <h3 class="h3 mb-16 t-20">{{ t("how.cards[1].title") }}</h3>
            <p class="paragraph t-18">
              {{ t("how.cards[1].description") }}
            </p>
          </div>
        </div>
        <div class="card box-shadow w-col w-col-4 w-col-stack">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba5743115299398ddb281a_icon-03.svg"
            loading="lazy"
            alt=""
            class="image-3 mb-32"
          />
          <div class="title">
            <h3 class="h3 mb-16 t-20">
              {{ t("how.cards[2].title") }}
            </h3>
            <p class="paragraph t-18">
              {{ t("how.cards[2].description") }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="integrations mb-80 mt-40">
      <p class="paragraph text-center dark-grey mb-24">
        <span class="bold-text">{{ t("how.integrations.heading") }}</span>
        <br />{{ t("how.integrations.subheading") }}
      </p>
      <div class="w-layout-blockcontainer logo-new-container w-container">
        <div class="w-layout-blockcontainer logo-adjst _3rd w-container">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba5d2faa465c7bfef46f28_gm.svg"
            loading="lazy"
            width="248"
            alt=""
            class="logos-integrations bm-8-copy"
          />
        </div>
        <div class="w-layout-blockcontainer logo-adjst _3rd w-container">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65c357c30b92619b9cd464d4_zoom_logo_new%20grey.svg"
            loading="lazy"
            width="90"
            alt=""
            class="logos-integrations zoom"
          />
        </div>
        <div class="w-layout-blockcontainer logo-adjst _3rd w-container">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba5d2f765fea86ade853aa_mt.png"
            loading="lazy"
            width="148"
            alt=""
            class="logos-integrations bm-8-copy"
          />
        </div>
      </div>
      <div class="w-layout-blockcontainer logo-new-container w-container">
        <div class="w-layout-blockcontainer logo-adjst no-pad w-container">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba5f1f9563f7d6525a82e3_calendarx2.png"
            loading="lazy"
            width="111"
            alt=""
            class="logos-integrations v2"
          />
        </div>
        <div class="w-layout-blockcontainer logo-adjst no-pad w-container">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65c52e03eda4f3354cb6d764_outlook-logo.png"
            loading="lazy"
            width="114"
            alt=""
            class="logos-integrations v2"
          />
        </div>
        <div class="w-layout-blockcontainer logo-adjst v2 w-container">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba5d2f3ac9268e33955951_hs.svg"
            loading="lazy"
            width="142"
            alt=""
            class="logos-integrations v2 smaller"
          />
        </div>
        <div class="w-layout-blockcontainer logo-adjst v2 w-container">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba5d2e0dafda26430927a4_sf.svg"
            loading="lazy"
            width="59"
            alt=""
            class="logos-integrations v2"
          />
        </div>
      </div>
      <div class="col-6 comingsoon w-row">
        <aside class="col-img-logo al-right no-margin centerlogo w-col w-col-6">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65bba8ce6542716c307e9fdd_Coming%20Soon_.svg"
            loading="lazy"
            alt=""
            class="logos-integrations comingsoon"
          />
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba5d2e07b975a54495a3ec_pipe.svg"
            loading="lazy"
            width="214"
            alt=""
            class="logos-integrations"
          />
        </aside>
      </div>

      <!-- 新增Telegram和Website集成图标 -->
      <div class="w-layout-blockcontainer logo-new-container w-container">
        <div class="w-layout-blockcontainer logo-adjst _3rd w-container">
          <OptimizedImage
            :src="telegramImg"
            alt="Telegram"
            class="logos-integrations bm-8-copy integration-icon"
            loading="lazy"
            sizes="(max-width: 480px) 140px, (max-width: 767px) 160px, 200px"
          />
        </div>
        <div class="w-layout-blockcontainer logo-adjst _3rd w-container">
          <OptimizedImage
            :src="websiteImg"
            alt="Website"
            class="logos-integrations bm-8-copy integration-icon"
            loading="lazy"
            sizes="(max-width: 480px) 140px, (max-width: 767px) 160px, 200px"
          />
        </div>
      </div>
    </div>
    <div class="bg-wave">
      <img
        src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba6145212d7957e10bb107_bg-waves.svg"
        loading="lazy"
        alt=""
        class="image-9"
      />
    </div>
  </section>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;
@use "sass:color";

.how {
  position: relative;
  padding: 0 0 0;
  overflow: hidden;
  margin-top: -20px;
  background-color: #edf0f3;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  text-align: center;

  @include mobile-only {
    padding: 0 20px;
  }
}

.mt-56 {
  margin-top: 56px;
}

.h2 {
  font-size: 48px;
  line-height: 1.2;
  font-weight: 800;
  margin-bottom: 40px;
  color: #111827;

  @media screen and (max-width: 991px) {
    font-size: 36px;
  }

  @include mobile-only {
    font-size: 32px;
  }
}

.smaller {
  font-size: 32px;
  color: #4b5563;

  @media screen and (max-width: 991px) {
    font-size: 28px;
  }

  @include mobile-only {
    font-size: 24px;
    margin-top: 10px;
    margin-bottom: 30px;
  }
}

.image-7 {
  position: relative;
  width: 100%;
  margin-bottom: -10px;
}

.image-8 {
  max-width: 60%;
  height: auto;
  margin-bottom: 60px;
  margin-left: auto;
  margin-right: auto;
  display: block;

  &.mobile {
    display: none;

    @include mobile-only {
      display: block;
      width: 100%;
      max-width: 100%;
      margin-left: auto;
      margin-right: auto;
      margin-bottom: 40px;
      padding: 0 10px;
    }
  }

  &.desktop {
    display: block;

    @include mobile-only {
      display: none;
    }
  }
}

.desktop {
  display: block;
}

.mobile {
  display: none;
}

.columns {
  display: flex;
  justify-content: space-between;
  margin: 0 -15px;

  @media screen and (max-width: 991px) {
    flex-direction: column;
  }

  &.col-80 {
    max-width: 1280px;
    margin-left: auto;
    margin-right: auto;

    @include mobile-only {
      padding: 0 10px;
    }
  }
}

.card {
  text-align: left;
  background-color: $color-white;
  border-radius: 20px;
  flex-direction: column;
  flex: 1;
  align-self: stretch;
  align-items: stretch;
  padding: 20px;
  display: flex;
  margin: 0 15px;

  @media screen and (max-width: 991px) {
    margin: 0 0 30px 0;
  }

  &.box-shadow {
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 32px;
    padding-top: 24px;
    padding-left: 32px;
    padding-right: 32px;
  }
}

.box-shadow {
  box-shadow: 0 8px 20px 2px rgba(238, 240, 243, 0.48);
}

.mb-32 {
  margin-bottom: 32px;
}

.h3 {
  font-size: 20px;
  line-height: 1.4;
  font-weight: 600;
  margin-bottom: 16px;
  color: #111827;
}

.t-18 {
  font-size: 18px;
  line-height: 1.6;
  color: #4b5563;
}

.integrations {
  margin: 20px 0 40px;

  &.mb-80.mt-40 {
    z-index: 2;
    position: relative;
    flex-direction: column;
    align-self: center;
    align-items: center;
    max-width: 1280px;
    padding-top: 40px;
    padding-bottom: 0;

    @media screen and (max-width: 991px) {
      padding-left: 10px;
      padding-right: 10px;
    }

    @include mobile-only {
      padding-left: 20px;
      padding-right: 20px;
    }
  }
}

.dark-grey {
  color: #4b5563;
}

.bold-text {
  font-weight: 600;
}

.w-layout-blockcontainer {
  &.logo-new-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 10px;
    gap: 5px;
    row-gap: 0;
  }

  &.logo-adjst {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
  }
}

.logos-integrations {
  max-width: 100%;
  height: auto;
}

.col-6 {
  &.comingsoon {
    display: flex;
    justify-content: center;
    margin-top: 0;
  }
}

.col-img-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.bg-wave {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.image-9 {
  width: 100%;
  height: auto;
  display: block;
  margin-bottom: 0;
  margin-top: 0;
}

.integration-icon {
  max-width: 200px;
  height: auto;
  transition: all 0.3s ease;

  @include mobile-only {
    max-width: 160px;
  }

  @include respond-to("small") {
    max-width: 140px;
  }
}

.w-layout-blockcontainer.logo-adjst._3rd {
  @include mobile-only {
    padding: 10px;
  }

  @include respond-to("small") {
    padding: 8px;
  }
}
</style>
