<script setup>
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const videoContainer = ref(null);
const videoLoaded = ref(false);
const videoElement = ref(null);

onMounted(() => {
  // 延迟添加动画类，确保在fade-up动画完成后开始文字变色动画
  setTimeout(() => {
    const animatedText = document.querySelector(".title-animated");
    if (animatedText) {
      animatedText.classList.add("start-animation");
    }
  }, 1500);

  // 设置视频懒加载
  if (videoContainer.value) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !videoLoaded.value) {
            loadVideo();
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: "50px 0px", // 提前50px开始加载
        threshold: 0.1,
      }
    );

    observer.observe(videoContainer.value);
  }
});

const loadVideo = () => {
  videoLoaded.value = true;

  // 确保视频元素加载后自动播放
  setTimeout(() => {
    if (videoElement.value) {
      videoElement.value.play().catch((error) => {
        console.log("Video autoplay failed:", error);
      });
    }
  }, 100);
};
</script>

<template>
  <section id="hero" class="hero home _2">
    <div class="animation-hero---desktop" data-aos="fade-up">
      <!-- 替换lottie动画为自定义CSS动画 -->
      <div class="animated-title">
        <div class="title-line">
          <span class="title-static">{{ t("hero.animated_title.part1") }}</span>
        </div>
        <div class="title-line">
          <span class="title-static">{{ t("hero.animated_title.part2") }}</span>
          <span class="title-animated">
            {{ t("hero.animated_title.part3") }}
          </span>
        </div>
      </div>
    </div>
    <div class="wrapper">
      <div
        class="container center center-mobile mb-56 _2 w-container"
        data-aos="fade-up"
        data-aos-delay="200"
      >
        <div class="title _2">
          <h1 class="h1 text-center mb-24 mobile-top-fix hidden-text">
            {{ t("hero.title") }}
          </h1>
          <p class="paragraph text-center mb-56 _2 _w-90">
            {{ t("hero.description") }}
          </p>
        </div>
        <div class="w-layout-blockcontainer container-3 _2 w-container">
          <a href="#" class="btn btn-secondary ml-32 w-inline-block">
            <img
              src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/6319cb9197221971b819f8ab_play-circle-line.svg"
              loading="lazy"
              alt=""
            />
            <p class="btn-text ml-8">{{ t("hero.buttons.watchVideo") }}</p>
          </a>
          <!-- <a
            id="Signup"
            href="https://insights.talka.ai/signup/?utm_source=website&utm_medium=button&utm_campaign=Main+Website+-+Sign+Up"
            class="btn btn-secondary line color w-button"
            >{{ t('hero.buttons.startFree') }}</a
          > -->
          <a
            id="BookDemoButton"
            target="_blank"
            href="https://talka.tw/chatdemo"
            class="hero-demo-button"
          >
            {{ t("hero.buttons.bookDemo") }}
          </a>
        </div>
      </div>
      <div
        ref="videoContainer"
        class="video"
        data-aos="fade-up"
        data-aos-delay="400"
      >
        <!-- 视频占位图 -->
        <div v-if="!videoLoaded" class="video-placeholder" @click="loadVideo">
          <img
            src="/src/assets/how_img1_srcset_pc1080.png"
            alt="Video preview"
            class="placeholder-image"
          />
          <div class="play-button-overlay">
            <svg
              width="80"
              height="80"
              viewBox="0 0 80 80"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="40" cy="40" r="40" fill="rgba(0,0,0,0.7)" />
              <polygon points="32,25 32,55 55,40" fill="white" />
            </svg>
          </div>
        </div>

        <!-- 实际视频 -->
        <video
          v-if="videoLoaded"
          ref="videoElement"
          class="hero__media-video"
          width="100%"
          autoplay
          muted
          loop
        >
          <source
            src="https://storage.googleapis.com/talka_official_website_assets/dashboard-filtering-v1.mp4"
            type="video/mp4"
          />
        </video>
      </div>
    </div>
    <div class="div-block bg">
      <img
        src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ba2aa659e3903d26a5ad48_linestk.svg"
        loading="lazy"
        alt=""
        class="image-6"
      />
    </div>
  </section>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;
@use "sass:color";

.hero {
  position: relative;
  padding-top: 120px;
  padding-bottom: 60px;
  overflow: hidden;
  min-height: 80vh;
}

.wrapper {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.container {
  text-align: center;
}

.title {
  max-width: 800px;
  margin: 0 auto;
}

.h1 {
  font-size: 48px;
  line-height: 1.2;
  font-weight: 800;
  margin: 0;
  color: #111827;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.paragraph {
  font-size: 18px;
  line-height: 1.6;
  color: #4b5563;
  margin: 24px 0 40px;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
}

.container-3 {
  display: flex;
  justify-content: center;
  gap: 16px;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 0.4s forwards;
}

.btn {
  text-align: center;
  letter-spacing: 0.5px;
  font-size: 14px;
  line-height: 18px;
  font-weight: $font-weight-semibold;
  padding: 15px 24px;
  border-radius: 5px;
  display: inline-block;
  transition: all 0.3s;
  text-decoration: none;
}

.btn-secondary {
  color: #2c3149;
  letter-spacing: 0.5px;
  background-color: rgba(0, 0, 0, 0);
  justify-content: center;
  align-items: center;
  padding: 14px 20px;
  font-size: 14px;
  font-weight: $font-weight-semibold;
  line-height: 18px;
  text-decoration: none;
  display: flex;

  &.line {
    border-style: solid;
    border-width: 1px;
    border-color: rgba(45, 55, 74, 0.2);
    border-radius: 5px;
    transition: all 0.3s;
    background-color: $color-white;

    &.color {
      color: $color-white;
      background-color: #002b55;
      border-color: #002b55;
      border-radius: 5px;
    }
  }
}

.hero-demo-button {
  background-color: $color-primary;
  color: $color-white;
  border: none;
  letter-spacing: 0.5px;
  justify-content: center;
  align-items: center;
  padding: 14px 60px;
  font-size: 14px;
  font-weight: $font-weight-semibold;
  line-height: 18px;
  text-decoration: none;
  display: flex;
  border-radius: 5px;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    background-color: #011936;
    border-style: none;
    border-radius: 4px;
  }
}

.animation-hero---desktop {
  width: auto; /* 修复水平滚动问题，替代 index.html 外部CSS中的100vw设置 */
}

.animated-title {
  font-size: 48px;
  font-weight: 800;
  line-height: 1.2;
  text-align: center;
  margin: 0 auto 40px;
  max-width: 900px;
  color: $color-black;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.title-line {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0;
  padding: 0;
  line-height: 1.3;
}

.title-static {
  color: $color-black;
  display: inline-block;
  margin-right: 4px;
}

.title-animated {
  position: relative;
  display: inline-block;
  color: $color-black;
  overflow: hidden;
  margin-left: 4px;

  &.start-animation {
    background: linear-gradient(
      to right,
      $color-primary,
      $color-primary 50%,
      $color-black 50%,
      $color-black
    );
    background-size: 200% 100%;
    background-position: 100% 0;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: textSlideAnimation 6s infinite;
  }

  &::before {
    display: none;
  }

  &.start-animation::before {
    display: none;
  }
}

@keyframes textSlideAnimation {
  0% {
    background-position: 100% 0;
  }
  30% {
    background-position: 0% 0;
  }
  80% {
    background-position: 0% 0;
  }
  80.1% {
    background-position: 100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.video {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hero__media-video {
  display: block;
  width: 100%;
}

.div-block {
  &.bg {
    position: absolute;
    left: 0%;
    top: 80%;
    right: 0%;
    bottom: 0%;
    z-index: 1;
    height: 40%;
    overflow: hidden;
  }
}

.image-6 {
  position: absolute;
  left: 0%;
  top: 0%;
  right: 0%;
  width: 100%;
  height: auto;
  min-width: 100%;
}

.hidden-text {
  display: none;
}

// 视频占位图样式
.video-placeholder {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;

  .placeholder-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .play-button-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    opacity: 0.9;

    &:hover {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.1);
    }
  }

  &:hover .placeholder-image {
    transform: scale(1.05);
    transition: transform 0.3s ease;
  }
}

.ml-32 {
  margin-left: 32px;
}

.ml-8 {
  margin-left: 8px;
}

.mb-24 {
  margin-bottom: $spacing-md;
}

.mb-56 {
  margin-bottom: $spacing-lg;
}

._w-90 {
  min-width: 100%;
  margin-left: auto;
  margin-right: auto;
}

/* 响应式设计 */
@include mobile-only {
  .hero {
    padding-top: 80px;
    padding-bottom: 40px;
  }

  .wrapper {
    padding: 0 20px;
  }

  .h1 {
    font-size: 32px;
  }

  .paragraph {
    font-size: 16px;
  }

  .container-3 {
    flex-direction: column;
    align-items: center;
  }

  .btn-secondary {
    margin: 8px 0;
  }

  .ml-32 {
    margin-left: 0;
  }

  .animated-title {
    font-size: 28px;
    padding: 0 20px;
    line-height: 1.3;
    margin-bottom: 30px;
  }

  .title-line {
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 5px;
    width: 100%;

    &:last-child {
      margin-top: 5px;
    }
  }

  .title-static,
  .title-animated {
    display: inline;
    white-space: normal;
  }

  .title-static {
    margin-right: 2px;
  }

  .title-animated {
    margin-left: 2px;
  }

  .div-block.bg {
    height: 200px;
    top: auto;
    bottom: 0;
  }

  .image-6 {
    height: 70%;
    object-fit: cover;
    transform: scale(1);
  }
}
</style>
