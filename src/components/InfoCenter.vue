<script setup>
import { ref, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import {
  formatChangelogDate,
  generateChangelogAnchorId,
} from "@/utils/dateFormatter.js";

const { t, tm, locale } = useI18n();

// 组件数据
const pageTitle = computed(() => t("infoCenter.pageTitle"));
const description = computed(() => t("infoCenter.description"));

// 更新日志数据 - 使用计算属性来支持国际化
const changelogData = computed(() => {
  const changelog = tm("infoCenter.changelog");
  return [
    {
      dateKey: "2025-07-17",
      date: formatChangelogDate("2025-07-17", locale.value),
      title: changelog["2025-07-17"].title,
      description: changelog["2025-07-17"].description,
      features: changelog["2025-07-17"].features,
    },
    {
      dateKey: "2025-06-28",
      date: formatChangelogDate("2025-06-28", locale.value),
      title: changelog["2025-06-28"].title,
      description: changelog["2025-06-28"].description,
      features: changelog["2025-06-28"].features,
    },
    {
      dateKey: "2025-06-15",
      date: formatChangelogDate("2025-06-15", locale.value),
      title: changelog["2025-06-15"].title,
      description: changelog["2025-06-15"].description,
      features: changelog["2025-06-15"].features,
    },
    {
      dateKey: "2025-05-18",
      date: formatChangelogDate("2025-05-18", locale.value),
      title: changelog["2025-05-18"].title,
      description: changelog["2025-05-18"].description,
      features: changelog["2025-05-18"].features,
    },
    {
      dateKey: "2025-04-22",
      date: formatChangelogDate("2025-04-22", locale.value),
      title: changelog["2025-04-22"].title,
      description: changelog["2025-04-22"].description,
      features: changelog["2025-04-22"].features,
    },
    {
      dateKey: "2025-03-30",
      date: formatChangelogDate("2025-03-30", locale.value),
      title: changelog["2025-03-30"].title,
      description: changelog["2025-03-30"].description,
      features: changelog["2025-03-30"].features,
    },
    {
      dateKey: "2025-02-14",
      date: formatChangelogDate("2025-02-14", locale.value),
      title: changelog["2025-02-14"].title,
      description: changelog["2025-02-14"].description,
      features: changelog["2025-02-14"].features,
    },
  ];
});

// 联系信息
const contactInfo = computed(() => ({
  title: t("infoCenter.contact.title"),
  email: t("infoCenter.contact.email"),
  description: t("infoCenter.contact.description"),
}));

// 滚动到指定日期的更新日志
const scrollToRelease = (dateKey) => {
  const elementId = generateChangelogAnchorId(dateKey);
  const element = document.getElementById(elementId);
  if (element) {
    // 计算滚动位置，考虑 sticky 导航栏的高度
    const offsetTop = element.offsetTop - 100; // 100px 为导航栏高度 + 间距
    window.scrollTo({
      top: offsetTop,
      behavior: "smooth",
    });
    // 更新 URL 但不刷新页面
    window.history.pushState({}, "", `#${elementId}`);
  }
};

// 检查 URL 锚点并自动滚动
const checkAndScrollToAnchor = () => {
  const hash = window.location.hash;
  if (hash && hash.startsWith("#release-")) {
    const element = document.getElementById(hash.substring(1));
    if (element) {
      // 延迟滚动，确保页面完全加载
      setTimeout(() => {
        const offsetTop = element.offsetTop - 100;
        window.scrollTo({
          top: offsetTop,
          behavior: "smooth",
        });
      }, 100);
    }
  }
};

// 页面加载完成后检查锚点
onMounted(() => {
  checkAndScrollToAnchor();
});
</script>

<template>
  <div class="info-center">
    <!-- 页面标题区域 -->
    <section class="header-section">
      <h1 class="page-title">{{ pageTitle }}</h1>
    </section>

    <!-- 描述区域 -->
    <section class="description-section">
      <p class="description">{{ description }}</p>
    </section>

    <!-- 更新日志区域 -->
    <section class="changelog-section">
      <div
        v-for="(changelog, index) in changelogData"
        :key="index"
        :id="generateChangelogAnchorId(changelog.dateKey)"
        class="changelog-item"
      >
        <!-- 左侧时间列 -->
        <div class="date-column">
          <div class="release-date" @click="scrollToRelease(changelog.dateKey)">
            {{ changelog.date }}
          </div>
        </div>

        <!-- 右侧内容列 -->
        <div class="content-column">
          <h2 class="release-title">{{ changelog.title }}</h2>
          <p class="release-description">{{ changelog.description }}</p>

          <div
            v-for="(feature, featureIndex) in changelog.features"
            :key="featureIndex"
            class="feature-item"
          >
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>

            <div v-if="feature.details" class="feature-details">
              <p
                v-for="(detail, detailIndex) in feature.details"
                :key="detailIndex"
                class="feature-detail"
              >
                {{ detail }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.info-center {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: #ffffff;
  padding: 80px 20px;
  max-width: 800px;
  margin: 0 auto;
  min-height: 100vh;

  // 页面标题区域
  .header-section {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;

    .page-title {
      font-size: 36px;
      font-weight: 700;
      color: #333333;
      text-align: center;
      margin: 0;
    }
  }

  // 描述区域
  .description-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 48px;

    .description {
      font-size: 16px;
      font-weight: 400;
      color: #666666;
      text-align: center;
      line-height: 1.6;
      margin: 0;
      max-width: 600px;
    }
  }

  // 更新日志区域
  .changelog-section {
    display: flex;
    flex-direction: column;
    gap: 48px;
    margin-bottom: 48px;

    .changelog-item {
      display: flex;
      flex-direction: row;
      gap: 40px;
      align-items: flex-start;

      // 左侧时间列
      .date-column {
        flex: 0 0 200px;
        display: flex;
        justify-content: flex-end;
        padding-right: 20px;
        position: sticky;
        top: 80px; // 导航栏高度 + 一些间距
        align-self: flex-start;
        z-index: 10;

        .release-date {
          font-size: 14px;
          font-weight: 600;
          color: #333333;
          margin: 0;
          text-align: right;
          background-color: #ffffff;
          padding: 8px 12px;
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border: 1px solid #e5e7eb;
          cursor: pointer;
          transition: all 0.2s ease;
          user-select: none;

          &:hover {
            background-color: #f8f9fa;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
            border-color: #d1d5db;
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  // 右侧内容列
  .content-column {
    flex: 1;
    border-left: 1px solid #e5e7eb;
    padding-left: 40px;

    .release-title {
      font-size: 24px;
      font-weight: 700;
      color: #333333;
      margin: 0 0 16px 0;
    }

    .release-description {
      font-size: 16px;
      font-weight: 400;
      color: #444444;
      line-height: 1.6;
      margin: 0 0 24px 0;
    }

    .feature-item {
      margin-bottom: 24px;

      .feature-title {
        font-size: 18px;
        font-weight: 600;
        color: #333333;
        margin: 0 0 16px 0;
      }

      .feature-description {
        font-size: 16px;
        font-weight: 400;
        color: #444444;
        line-height: 1.6;
        margin: 0 0 16px 0;
      }

      .feature-details {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .feature-detail {
          font-size: 16px;
          font-weight: 400;
          color: #444444;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
}
// 底部联系区域
.contact-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: auto;
  padding-top: 48px;

  .contact-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    max-width: 400px;
    width: 100%;

    .contact-title {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      margin: 0 0 8px 0;
    }

    .contact-email {
      font-size: 16px;
      font-weight: 400;
      color: #007bff;
      text-decoration: underline;
      display: block;
      margin: 0 0 8px 0;

      &:hover {
        color: #0056b3;
      }
    }

    .contact-description {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin: 0;
    }
  }
}

// 移动端适配
@include mobile-only {
  .info-center {
    padding: 60px 16px;

    .header-section {
      .page-title {
        font-size: 28px;
      }
    }

    .description-section {
      .description {
        font-size: 14px;
      }
    }

    .changelog-section {
      // 移动端改为垂直布局
      .changelog-item {
        flex-direction: column;
        gap: 16px;

        .date-column {
          flex: none;
          justify-content: flex-start;
          padding-right: 0;
          position: sticky;
          top: 60px; // 移动端导航栏通常更小
          align-self: flex-start;

          .release-date {
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #333333;
            background-color: #ffffff;
            padding: 6px 10px;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;

            &:hover {
              background-color: #f8f9fa;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              transform: translateY(-1px);
              border-color: #d1d5db;
            }

            &:active {
              transform: translateY(0);
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }
          }
        }

        .content-column {
          border-left: none;
          padding-left: 0;
          border-top: 1px solid #e5e7eb;
          padding-top: 16px;

          .release-title {
            font-size: 20px;
          }

          .feature-item {
            .feature-title {
              font-size: 16px;
            }

            .release-description,
            .feature-description {
              font-size: 14px;
            }

            .feature-details {
              .feature-detail {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
