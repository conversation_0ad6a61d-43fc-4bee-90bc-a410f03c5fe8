<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-21 10:45:31
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-03-21 15:43:36
 * @FilePath     : /talka-clone/src/components/ContactModal.vue
 * @Description  :
 * Copyright 2025 <PERSON>, All Rights Reserved.
 * 2025-03-21 10:45:31
-->
<template>
  <section id="cta-footer" class="contact-modal _2">
    <div class="wrapper modal-wrapper m2">
      <div class="container w-container">
        <div
          data-w-id="90ac344c-f9f3-371e-1afc-e604bb136128"
          class="close-modal w-embed"
        >
          <svg
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_670_58044)">
              <path
                d="M11.9997 10.586L16.9497 5.63599L18.3637 7.04999L13.4137 12L18.3637 16.95L16.9497 18.364L11.9997 13.414L7.04974 18.364L5.63574 16.95L10.5857 12L5.63574 7.04999L7.04974 5.63599L11.9997 10.586Z"
                fill="CurrentColor"
              />
            </g>
            <defs>
              <clipPath id="clip0_670_58044">
                <rect width="24" height="24" />
              </clipPath>
            </defs>
          </svg>
        </div>
        <div class="title title-2 modal-title mb-32 modal-t">
          <h2 class="modal-title">{{ $t("contactModal.title") }}</h2>
          <p class="paragraph mb-24 text-center-mobile modal-paragraph">
            {{ $t("contactModal.description") }}
          </p>
        </div>
        <div class="w-form">
          <form
            id="wf-form-"
            name="wf-form-"
            data-name=""
            method="post"
            class="form"
            data-wf-page-id="62348721c1221a9076f09611"
            data-wf-element-id="90ac344c-f9f3-371e-1afc-e604bb13612f"
          >
            <div class="form-field mb-16 modal big-mobile">
              <label for="Full-Name" class="field-label-default modal-field">{{
                $t("contactModal.form.fullName.label")
              }}</label>
              <input
                class="input modal-input w-input"
                maxlength="256"
                name="Full-Name"
                data-name="Full Name"
                :placeholder="$t('contactModal.form.fullName.placeholder')"
                type="text"
                id="Full-Name"
                required=""
              />
            </div>
            <div class="form-field mb-16 modal big-mobile">
              <label for="E-mail" class="field-label-default modal-field">{{
                $t("contactModal.form.email.label")
              }}</label>
              <input
                class="input modal-input w-input"
                maxlength="256"
                name="E-mail"
                data-name="E-mail"
                :placeholder="
                  $t('contactModal.form.email.placeholder') + '<EMAIL>'
                "
                type="email"
                id="E-mail"
                required=""
              />
            </div>
            <div class="form-field mb-16 modal">
              <label for="Company" class="field-label-default modal-field">{{
                $t("contactModal.form.company.label")
              }}</label>
              <input
                class="input modal-input w-input"
                maxlength="256"
                name="Company"
                data-name="Company"
                :placeholder="$t('contactModal.form.company.placeholder')"
                type="text"
                id="Company"
                required=""
              />
            </div>
            <div class="form-field mb-16 modal">
              <label for="job-title" class="field-label-default modal-field">{{
                $t("contactModal.form.jobTitle.label")
              }}</label>
              <input
                class="input modal-input w-input"
                maxlength="256"
                name="job-title"
                data-name="job-title"
                :placeholder="$t('contactModal.form.jobTitle.placeholder')"
                type="text"
                id="job-title"
                required=""
              />
            </div>
            <div class="form-field mb-16 modal big-mobile">
              <label for="phone" class="field-label-default modal-field">{{
                $t("contactModal.form.phone.label")
              }}</label>
              <input
                class="input modal-input w-input"
                maxlength="256"
                name="Phone-number"
                data-name="Phone-number"
                :placeholder="$t('contactModal.form.phone.placeholder')"
                type="tel"
                id="phone"
              />
            </div>
            <div class="form-field mb-16 modal big-mobile">
              <label
                for="your-message"
                class="field-label-default modal-field"
                >{{ $t("contactModal.form.message.label") }}</label
              >
              <textarea
                id="your-message"
                name="your-message"
                maxlength="5000"
                data-name="your-message"
                :placeholder="$t('contactModal.form.message.placeholder')"
                class="input modal-input w-input"
              ></textarea>
            </div>
            <label class="w-checkbox checkbox-field mb-32 modal">
              <input
                id="checkbox-2"
                type="checkbox"
                name="checkbox-2"
                data-name="Checkbox 2"
                class="w-checkbox-input checkbox"
              />
              <span
                class="paragraph modal-consent w-form-label"
                for="checkbox-2"
                >{{ $t("contactModal.form.consent") }}</span
              >
            </label>
            <input
              type="submit"
              data-wait="Please wait..."
              class="btn btn-cto modal w-button"
              :value="$t('contactModal.form.submit')"
            />
          </form>
          <div class="w-form-done">
            <div>{{ $t("contactModal.formStatus.success") }}</div>
          </div>
          <div class="w-form-fail">
            <div>{{ $t("contactModal.formStatus.error") }}</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { useI18n } from "vue-i18n";
const { t } = useI18n();
</script>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.contact-modal {
  padding: 60px 0;
  background-color: #f5f8ff;

  @include mobile-only {
    padding: 40px 0;
  }
}

.wrapper.modal-wrapper {
  max-width: 960px;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.container {
  position: relative;
}

.close-modal {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  color: #333;
  transition: transform 0.3s ease;

  &:hover {
    transform: rotate(90deg);
  }
}

.modal-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
  text-align: center;

  @include mobile-only {
    font-size: 24px;
  }
}

.modal-paragraph {
  text-align: center;
  margin-bottom: 24px;

  @include mobile-only {
    font-size: 14px;
  }
}

.form {
  max-width: 600px;
  margin: 0 auto;
}

.form-field {
  margin-bottom: 16px;
}

.field-label-default {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
}

.input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;

  &:focus {
    border-color: $color-primary;
    outline: none;
  }
}

textarea.input {
  min-height: 120px;
  resize: vertical;
}

.checkbox-field {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
}

.btn.btn-cto {
  display: block;
  width: 100%;
  padding: 12px;
  background-color: $color-primary;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: darken($color-primary, 10%);
  }

  @include mobile-only {
    padding: 10px;
    font-size: 14px;
  }
}

.w-form-done,
.w-form-fail {
  padding: 20px;
  margin-top: 20px;
  border-radius: 4px;
  text-align: center;
}

.w-form-done {
  background-color: #d4edda;
  color: #155724;
}

.w-form-fail {
  background-color: #f8d7da;
  color: #721c24;
}

// 这个类在模板中有使用
.text-center-mobile {
  @include mobile-only {
    text-align: center;
  }
}

// 这个类在模板中有使用
.big-mobile {
  @include mobile-only {
    margin-bottom: 20px;
  }
}
</style>
