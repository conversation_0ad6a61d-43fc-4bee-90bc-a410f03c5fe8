<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-04-08 11:02:52
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 16:33:50
 * @FilePath     : /src/components/price/PriceHeader.vue
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-04-08 11:02:52
-->
<script setup>
import OptimizedImage from "@/components/OptimizedImage.vue";
import logoLight from "@/assets/logo_light.png";
</script>

<template>
  <div class="price-header">
    <div class="quotation-wrapper">
      <img
        :src="logoLight"
        alt="Talka Logo"
        class="logo-image"
        loading="eager"
      />
      <div class="price-title-box">
        <h1 class="title-main">{{ $t("price.pricingTitle") }}</h1>
        <h2 class="title-sub">{{ $t("price.pricingSubTitle") }}</h2>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/mixins" as *;

.price-header {
  padding: 20px 0;
  width: 100%;

  .quotation-wrapper {
    width: 100%;
    max-width: 1200px;

    .logo-image {
      max-width: 140px;
      height: auto;
      margin-bottom: 20px;
    }

    .logo-box {
      display: flex;
      margin-bottom: 20px;

      .letter {
        display: inline-block;
        font-size: 64px;
        font-weight: 800;
        color: #f6ae2d;
        letter-spacing: -3px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);

        &:nth-child(3) {
          color: #25c485;
        }

        &:nth-child(4) {
          color: #e01e5a;
        }
      }
    }

    .price-title-box {
      margin-bottom: 40px;
      .title-main {
        font-size: 48px;
        font-weight: 700;
        margin: 0;
        line-height: 1.2;
        color: white;
      }

      .title-sub {
        font-size: 32px;
        font-weight: 600;
        color: white;
      }
    }
  }

  @include mobile-only {
    padding: 15px 0;

    .quotation-wrapper {
      .logo-box {
        margin-bottom: 15px;

        .letter {
          font-size: 48px;
        }
      }

      .price-title-box {
        .title-main {
          font-size: 32px;
        }

        .title-sub {
          font-size: 24px;
        }
      }
    }
  }
}
</style>
