<script setup>
import { standardPriceData } from "./PriceTableData";
import { computed } from "vue";
import { useI18n } from "vue-i18n";

const { locale } = useI18n();
const isEnglish = computed(() => {
  return locale.value === "en";
});
</script>

<template>
  <div class="standard-price-container">
    <!-- 上部分：标题 -->
    <div class="title-section">
      <template v-if="isEnglish">
        <div class="header-title">
          <div class="title-word">
            {{ $t("price.standardPrice.firstWord") }}
          </div>
          <div class="title-word">
            {{ $t("price.standardPrice.secondWord") }}
          </div>
        </div>
      </template>
      <template v-else>
        <div class="title-chinese">
          {{ $t("price.standardPrice.subtitle") }}
        </div>
      </template>
    </div>

    <!-- 中部分：价格表 -->
    <div class="price-table-wrapper">
      <table class="standard-table">
        <thead>
          <tr class="header-row">
            <th colspan="2" class="version-header">
              <h3 class="table-title">
                {{ $t("price.standardPrice.basicVersion") }}
              </h3>
              <p class="version-description">
                {{ $t("price.standardPrice.basicDesc") }}
              </p>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr class="price-row price-label-row">
            <td class="seat-header">{{ $t("price.seats") }}</td>
            <td class="price-header">{{ $t("price.price") }}</td>
          </tr>
          <tr
            v-for="row in standardPriceData"
            :key="row.seats"
            class="price-row"
          >
            <td class="seat-number">{{ row.seats }}</td>
            <td class="price-value">
              {{ row.price }}
              <span class="price-unit">
                <span class="currency">$</span>
                <span class="period">{{ $t("price.period") }}</span>
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 下部分：描述信息 -->
    <div class="description-section">
      <div class="talka-slogan">
        <div class="talka-name">{{ $t("price.description.companyName") }}</div>
        <div class="talka-slogan1">{{ $t("price.description.slogan1") }}</div>
        <div class="talka-slogan2">{{ $t("price.description.slogan2") }}</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.standard-price-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 25%;
  position: relative;

  @media screen and (max-width: 991px) {
    max-width: 100%;
  }

  // 上部分：标题样式
  .title-section {
    color: #f6ae2d;
    padding-bottom: 40px;
    height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    font-size: 32px;

    .header-title {
      display: flex;
      flex-direction: column;
      font-weight: 700;
      line-height: 1.1;

      .title-word {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .title-chinese {
      font-size: 32px;
      font-weight: 600;
      margin-top: 5px;
      color: white;
    }
  }

  // 下部分：描述信息样式
  .description-section {
    height: 240px;
    margin-top: 15px;
    padding: 0;
    color: #19283b;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    @media screen and (max-width: 767px) {
      height: auto;
    }

    .talka-slogan {
      margin-top: 15px;
      text-align: left;

      .talka-name {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 5px;
        color: #f6ae2d;
      }

      .talka-slogan1,
      .talka-slogan2 {
        font-size: 18px;
        font-weight: 700;
        color: #f6ae2d;
      }
    }
  }
}

// 表格样式修改
.price-table-wrapper {
  flex: 1;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: transparent;

  .standard-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    color: white;

    thead {
      background-color: rgba(37, 196, 133, 0.9);

      .header-row {
        border-bottom: 2px solid white;
      }
      .version-header {
        padding: 8px 30px;
        text-align: left;
        height: 68px;

        .table-title {
          font-size: 24px;
          line-height: 24px;
          font-weight: 600;
          margin: 0;
        }

        .version-description {
          font-size: 14px;
          color: black;
          font-weight: 600;
          margin: 0;
        }
      }
    }

    tbody {
      .price-row {
        border-bottom: none;
        position: relative;

        &:last-child {
          border-bottom: none;
        }

        &:not(:last-child) {
          background-image: linear-gradient(
            to right,
            transparent 30px,
            white 30px,
            white calc(100% - 30px),
            transparent calc(100% - 30px)
          );
          background-position: 0 100%;
          background-size: 100% 1px;
          background-repeat: no-repeat;
        }

        &.price-label-row {
          background-color: transparent;
        }

        td {
          padding: 0 15px;
          height: 50px;
          font-size: 22px;
          text-align: center;

          &.seat-header,
          &.price-header {
            padding: 12px 15px;
            font-size: 20px;
            font-weight: 800;
            text-align: center;
          }

          &.seat-number {
            font-weight: 700;
            transform: skewX(-12deg);
          }

          &.price-value {
            font-weight: 700;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .price-unit {
              display: inline-flex;
              flex-direction: column;
              font-size: 12px;
              margin-left: 2px;
              line-height: 1;
              font-weight: normal;
              position: relative;
              width: 28px;

              .currency {
                font-size: 14px;
                position: absolute;
                left: 2px;
                top: 50%;
                transform: translateY(-50%);
              }

              .period {
                position: absolute;
                right: 0;
                bottom: -10px;
              }
            }
          }
        }
      }
    }

    @media screen and (max-width: 991px) {
      tbody .price-row td {
        height: auto;
        padding: 8px 15px;
      }
    }

    @media screen and (max-width: 767px) {
      thead {
        .version-header {
          padding: 5px 15px;
          height: 60px;

          .table-title {
            font-size: 18px;
            line-height: 18px;
          }

          .version-description {
            font-size: 12px;
          }
        }
      }

      tbody .price-row td {
        padding: 5px 15px;
        font-size: 16px;
      }
    }
  }
}
</style>
