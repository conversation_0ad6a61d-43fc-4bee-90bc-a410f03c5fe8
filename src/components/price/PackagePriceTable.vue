<script setup>
import { packagePriceData } from "./PriceTableData";
import { computed, ref, onMounted, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import { useResponsive } from "@/utils/responsive";

const { locale } = useI18n();
const isEnglish = computed(() => {
  return locale.value === "en";
});

// 使用响应式工具获取设备类型
const { isMobile } = useResponsive();

// 根据设备类型和语言选择合适的版本类型
const versionType = computed(() => {
  return isMobile.value ? "short" : "full";
});
</script>

<template>
  <div class="package-price-container">
    <!-- 上部分：标题 -->
    <div class="title-section">
      <template v-if="isEnglish">
        <div class="header-title">
          <div class="title-word">Package</div>
          <div class="title-word">Price</div>
        </div>
        <div class="limited-offer">Limited-Time Offer</div>
      </template>
      <template v-else>
        <div>
          <span class="title-chinese">
            {{ $t("price.packagePrice.title") }}
          </span>
          <span class="chinese-limited-offer">
            {{ $t("price.packagePrice.limitedOffer") }}
          </span>
        </div>
      </template>
    </div>

    <!-- 中部分：价格表 -->
    <div class="price-table-wrapper">
      <table class="package-table">
        <colgroup>
          <col class="seat-col" width="80" />
          <col class="version-col" width="33.33%" />
          <col class="version-col" width="33.33%" />
          <col class="version-col" width="33.33%" />
        </colgroup>
        <thead>
          <tr class="header-row">
            <th class="seat-header">{{ $t("price.seats") }}</th>
            <th class="version-header">
              <h3 class="table-title">
                {{ $t(`price.packagePrice.versions.0.${versionType}.name`) }}
              </h3>
              <p class="version-description">
                {{
                  $t(`price.packagePrice.versions.0.${versionType}.description`)
                }}
              </p>
            </th>
            <th class="version-header">
              <h3 class="table-title">
                {{ $t(`price.packagePrice.versions.1.${versionType}.name`) }}
              </h3>
              <p class="version-description">
                {{
                  $t(`price.packagePrice.versions.1.${versionType}.description`)
                }}
              </p>
            </th>
            <th class="version-header">
              <h3 class="table-title">
                {{ $t(`price.packagePrice.versions.2.${versionType}.name`) }}
              </h3>
              <p class="version-description">
                {{
                  $t(`price.packagePrice.versions.2.${versionType}.description`)
                }}
              </p>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr class="price-row price-label-row">
            <td class="seat-number" data-value=""></td>
            <td
              v-for="(_, index) in [0, 1, 2]"
              :key="index"
              :class="['price-cell', index === 2 ? 'special' : '']"
            >
              <p class="price-label">
                <span class="special-text">
                  {{ $t("price.packagePrice.special") }}
                </span>
                <span class="price-text">
                  {{ $t("price.packagePrice.price") }}
                </span>
              </p>
            </td>
          </tr>
          <tr
            v-for="row in packagePriceData"
            :key="row.seats"
            class="price-row"
          >
            <td class="seat-number" :data-value="row.seats">{{ row.seats }}</td>
            <td class="price-cell">
              {{ row.prices[0] }}
              <span class="price-unit">
                <span class="currency">$</span>
                <span class="period">{{ $t("price.period") }}</span>
              </span>
            </td>
            <td class="price-cell">
              {{ row.prices[1] }}
              <span class="price-unit">
                <span class="currency">$</span>
                <span class="period">{{ $t("price.period") }}</span>
              </span>
            </td>
            <td class="price-cell special">
              {{ row.prices[2] }}
              <span class="price-unit">
                <span class="currency">$</span>
                <span class="period">{{ $t("price.period") }}</span>
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 下部分：描述信息 -->
    <div class="description-section">
      <div class="talka-system">
        <div class="talka-name">{{ $t("price.system.name") }}</div>
        <div class="talka-subtitle">{{ $t("price.system.subtitle") }}</div>
        <div class="talka-desc">
          {{ $t(`price.system.description`) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.package-price-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;

  @media screen and (max-width: 991px) {
    max-width: 100%;
    margin-top: 30px;
  }

  // 上部分：标题样式
  .title-section {
    color: #f6ae2d;
    padding-bottom: 40px;
    height: 150px;
    font-size: 32px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    .header-title {
      display: flex;
      flex-direction: column;
      font-weight: 700;
      line-height: 1.1;

      .title-word {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .limited-offer {
      font-size: 24px;
      font-weight: 600;
      margin-top: 5px;
      color: #e01e5a;
      @media screen and (max-width: 320px) {
        font-size: 20px;
      }
    }

    .title-chinese {
      font-size: 32px;
      font-weight: 600;
      margin-top: 5px;
      color: white;
    }

    .chinese-limited-offer {
      font-size: 32px;
      font-weight: 600;
      color: #e01e5a;
      margin-left: 30px;
      @media screen and (max-width: 320px) {
        font-size: 24px;
        margin-left: 10px;
      }
    }
  }

  // 下部分：描述信息样式
  .description-section {
    height: 240px;
    margin-top: 15px;
    padding: 0;
    color: #19283b;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    @media screen and (max-width: 767px) {
      height: auto;
    }
    .talka-system {
      margin-top: 15px;
      padding-left: 50px;
      text-align: left;
      color: white;

      @media screen and (max-width: 767px) {
        padding-left: 0;
      }

      .talka-name {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 5px;
        margin-bottom: 15px;
      }

      .talka-subtitle {
        font-size: 18px;
        font-weight: 700;
        margin-bottom: 20px;
      }
      .talka-desc {
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
}

// 表格样式
.price-table-wrapper {
  flex: 1;
  border-radius: 10px;
  overflow: hidden;
  background-color: transparent;
  max-width: 100%;
  border: 2px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;

  @media screen and (max-width: 767px) {
    font-size: 14px;
  }

  .package-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    border: none;
    height: 100%;
    display: flex;
    flex-direction: column;

    thead {
      background-color: rgba(224, 30, 90, 0.9);
      flex-shrink: 0;

      .header-row {
        border-bottom: 2px solid white;
      }

      tr {
        display: flex;
        border-bottom: none;
      }

      th {
        height: 68px;
        padding: 8px;
        border-right: 2px solid white;
        text-align: center;
        vertical-align: middle;
        display: flex;
        flex-direction: column;
        justify-content: center;

        @media screen and (max-width: 767px) {
          padding: 5px;
          height: 60px;
        }

        &:first-child {
          width: 80px;
          flex: 0 0 80px;

          @media screen and (max-width: 767px) {
            width: 60px;
            flex: 0 0 60px;
          }
        }

        &:not(:first-child) {
          flex: 1;
        }

        &:last-child {
          border-right: none;
        }

        .table-title {
          font-size: 24px;
          line-height: 24px;
          font-weight: 600;
          margin: 0;
          color: white;

          @media screen and (max-width: 767px) {
            font-size: 18px;
            line-height: 18px;
          }
          @media screen and (max-width: 320px) {
            font-size: 14px;
            line-height: 14px;
          }
        }

        .version-description {
          font-size: 14px;
          font-weight: 600;
          margin: 0;

          @media screen and (max-width: 767px) {
            font-size: 12px;
          }
          @media screen and (max-width: 320px) {
            font-size: 12px;
            line-height: 12px;
          }
        }
      }
    }

    tbody {
      flex: 1;
      display: flex;
      flex-direction: column;

      tr {
        display: flex;
        flex: 1;
        // border-bottom: 1px solid rgba(255, 255, 255, 0.4);

        &:not(:last-child) {
          background-image: linear-gradient(
            to right,
            transparent 3%,
            white 3%,
            white 95%,
            transparent 95%
          );
          background-position: 0 100%;
          background-size: 100% 1px;
          background-repeat: no-repeat;
        }

        &:last-child {
          border-bottom: none;
        }

        &.price-label-row {
          border-top: none;
        }
      }

      td {
        padding: 8px;
        border-right: 2px solid white;
        text-align: center;
        vertical-align: middle;
        display: flex;
        justify-content: center;
        align-items: center;

        @media screen and (max-width: 767px) {
          padding: 5px;
        }

        &:first-child {
          width: 80px;
          flex: 0 0 80px;

          @media screen and (max-width: 767px) {
            width: 60px;
            flex: 0 0 60px;
          }
        }

        &:not(:first-child) {
          flex: 1;
        }

        &:last-child {
          border-right: none;
        }

        &.seat-number {
          font-size: 22px;
          font-weight: 700;
          color: transparent;
          position: relative;
          overflow: visible;

          &:after {
            content: attr(data-value);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            transform: skewX(-12deg);
            color: white;
          }

          @media screen and (max-width: 767px) {
            font-size: 16px;
          }
        }

        &.price-cell {
          font-size: 22px;
          font-weight: 700;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          @media screen and (max-width: 767px) {
            font-size: 16px;
          }
          @media screen and (max-width: 320px) {
            font-size: 12px;
          }

          &.special {
            color: #f39c12;
          }

          .price-label {
            margin: 0;

            .special-text {
              color: #e01e5a; // 红色
              font-weight: 700;
            }

            .price-text {
              color: white; // 白色
              font-weight: 500;
            }
          }

          .price-unit {
            display: inline-flex;
            flex-direction: column;
            font-size: 12px;
            margin-left: 2px;
            line-height: 1;
            font-weight: normal;
            position: relative;
            width: 28px;
            height: 22px;
            align-self: center;
            color: white;

            .currency {
              font-size: 14px;
              position: absolute;
              left: 2px;
              top: 2px;
              color: white;
            }

            .period {
              position: absolute;
              right: 0;
              bottom: 0;
              color: white;
            }

            @media screen and (max-width: 767px) {
              font-size: 10px;
              width: 22px;
              height: 16px;

              .currency {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
