<!DOCTYPE html>
<html lang="zh_TW">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1" />
    <meta name="turbolinks-cache-control" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <script
      src="/assets/assets/portal-CFPZzes-.js"
      crossorigin="anonymous"
      type="module"
    ></script>
    <link
      rel="modulepreload"
      href="/assets/assets/_commonjsHelpers-BosuxZz1.js"
      as="script"
      crossorigin="anonymous"
    />
    <link
      rel="modulepreload"
      href="/assets/assets/_plugin-vue_export-helper-FFbhGeGD.js"
      as="script"
      crossorigin="anonymous"
    />
    <link
      rel="modulepreload"
      href="/assets/assets/vue-dompurify-html-DmDMP3XS.js"
      as="script"
      crossorigin="anonymous"
    />
    <link
      rel="modulepreload"
      href="/assets/assets/module-CCa4fuUi.js"
      as="script"
      crossorigin="anonymous"
    />
    <link
      rel="modulepreload"
      href="/assets/assets/useKeyboardNavigableList-JSRToARb.js"
      as="script"
      crossorigin="anonymous"
    />
    <link
      rel="modulepreload"
      href="/assets/assets/useMessageFormatter-BqCOQynw.js"
      as="script"
      crossorigin="anonymous"
    />
    <link
      rel="modulepreload"
      href="/assets/assets/axios-upsvKRUO.js"
      as="script"
      crossorigin="anonymous"
    />
    <link
      rel="modulepreload"
      href="/assets/assets/colorHelper-DkkSNPxc.js"
      as="script"
      crossorigin="anonymous"
    />
    <link
      rel="modulepreload"
      href="/assets/assets/Icon-BjIvgoI2.js"
      as="script"
      crossorigin="anonymous"
    />
    <link
      rel="stylesheet"
      href="/assets/assets/portal-BVaYb88c.css"
      media="screen"
    />
    <style>
      #appearance-dropdown[data-current-theme="system"]
        .check-mark-icon.light-theme,
      #appearance-dropdown[data-current-theme="system"]
        .check-mark-icon.dark-theme,
      #appearance-dropdown[data-current-theme="dark"]
        .check-mark-icon.light-theme,
      #appearance-dropdown[data-current-theme="dark"]
        .check-mark-icon.system-theme,
      #appearance-dropdown[data-current-theme="light"]
        .check-mark-icon.dark-theme,
      #appearance-dropdown[data-current-theme="light"]
        .check-mark-icon.system-theme {
        display: none;
      }
    </style>
    <meta name="csrf-param" content="authenticity_token" />
    <meta
      name="csrf-token"
      content="_cNVj0dUQfajPSR4XWvkH7IbctMoOibH8peVzeRzxqhY10-QNamH_lLWX3-HHprbVW5lzaFQhOaUilnqZoRTQw"
    />
    <title></title>

    <script>
      if (
        localStorage.theme === "dark" ||
        (!("theme" in localStorage) &&
          window.matchMedia("(prefers-color-scheme: dark)").matches)
      ) {
        // we can use document.body here but that would mean pushing this script inside the body
        // since the body is not created yet. This is done to avoid FOUC, at a tiny cost of Time to Interactive
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
        document.documentElement.classList.add("light");
      }
    </script>
  </head>
  <body>
    <div id="portal" class="antialiased">
      <main
        class="flex flex-col min-h-screen bg-white main-content dark:bg-slate-900"
        role="main"
      >
        <header
          class="sticky top-0 z-50 w-full bg-white shadow-sm dark:bg-slate-900"
        >
          <nav class="flex max-w-5xl px-4 mx-auto md:px-8" aria-label="Top">
            <div class="flex items-center w-full py-5 overflow-hidden">
              <a
                href="/hc/description/en"
                class="flex items-center h-10 text-lg font-semibold text-slate-900 dark:text-white"
              >
                説明中心
              </a>
            </div>

            <div class="flex items-center justify-between gap-2 sm:gap-5">
              <div
                class="relative flex-grow flex-shrink-0 px-1 py-2 cursor-pointer"
              >
                <button
                  id="toggle-appearance"
                  class="flex justify-between min-w-[76px] flex-row items-center stroke-slate-700 dark:stroke-slate-200 text-slate-800 dark:text-slate-100 gap-1"
                  type="button"
                >
                  <div
                    data-theme="system"
                    class="flex-row items-center gap-1 theme-button hidden"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 4H4C3.44772 4 3 4.44773 3 5.00002V10.0001C3 10.5524 3.44772 11.0002 4 11.0002H12C12.5523 11.0002 13 10.5524 13 10.0001V5.00002C13 4.44773 12.5523 4 12 4Z"
                        stroke-width="1.00001"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M6 13H10"
                        stroke-width="1.00001"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M8 11V13"
                        stroke-width="1.00001"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>

                    <span class="text-sm font-medium">系統</span>
                  </div>
                  <div
                    data-theme="light"
                    class="flex-row items-center gap-1 theme-button hidden"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8.0001 10.3998C9.32559 10.3998 10.4001 9.32529 10.4001 7.99982C10.4001 6.67436 9.32559 5.59985 8.0001 5.59985C6.67462 5.59985 5.6001 6.67436 5.6001 7.99982C5.6001 9.32529 6.67462 10.3998 8.0001 10.3998Z"
                        stroke-width="1.19999"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M8 2V3.19998"
                        stroke-width="1.19999"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M8 12.8V14"
                        stroke-width="1.19999"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M3.75806 3.75781L4.60406 4.6038"
                        stroke-width="1.19999"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11.396 11.3958L12.242 12.2417"
                        stroke-width="1.19999"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M2 7.99976H3.2"
                        stroke-width="1.19999"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M12.8 7.99976H14.0001"
                        stroke-width="1.19999"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M4.60406 11.3958L3.75806 12.2417"
                        stroke-width="1.19999"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M12.242 3.75781L11.396 4.6038"
                        stroke-width="1.19999"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>

                    <span class="text-sm font-medium">明亮</span>
                  </div>
                  <div
                    data-theme="dark"
                    class="flex-row items-center gap-1 theme-button hidden"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 3C7.33696 3.66304 6.96447 4.56232 6.96447 5.5C6.96447 6.43768 7.33696 7.33696 8 8C8.66304 8.66304 9.56232 9.03553 10.5 9.03553C11.4377 9.03553 12.337 8.66304 13 8C13 8.9889 12.7068 9.9556 12.1573 10.7778C11.6079 11.6001 10.827 12.241 9.91342 12.6194C8.99979 12.9978 7.99446 13.0969 7.02455 12.9039C6.05465 12.711 5.16373 12.2348 4.46447 11.5355C3.76521 10.8363 3.289 9.94535 3.09608 8.97545C2.90315 8.00555 3.00217 7.00021 3.3806 6.08658C3.75904 5.17295 4.39991 4.39206 5.22215 3.84265C6.0444 3.29324 7.01109 3 8 3Z"
                        stroke-width="1.11111"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>

                    <span class="text-sm font-medium">深色</span>
                  </div>
                  <div class="flex items-center px-1 pointer-events-none">
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M3 4.5L6 7.5L9 4.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </div>
                </button>
                <div
                  id="appearance-dropdown"
                  data-current-theme=""
                  class="absolute flex-col w-32 h-auto bg-white border border-solid rounded dark:bg-slate-900 top-9 right-1 border-slate-100 dark:border-slate-800"
                  aria-hidden="true"
                  style="display: none"
                  data-dropdown="appearance-dropdown"
                >
                  <button
                    id="toggle-theme-button"
                    data-theme="system"
                    class="flex flex-row items-center justify-between gap-1 px-2 py-2 border-b border-solid border-slate-100 dark:border-slate-800 stroke-slate-700 dark:stroke-slate-200 text-slate-800 dark:text-slate-100"
                  >
                    <div class="flex flex-row items-center gap-1">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 4H4C3.44772 4 3 4.44773 3 5.00002V10.0001C3 10.5524 3.44772 11.0002 4 11.0002H12C12.5523 11.0002 13 10.5524 13 10.0001V5.00002C13 4.44773 12.5523 4 12 4Z"
                          stroke-width="1.00001"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M6 13H10"
                          stroke-width="1.00001"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M8 11V13"
                          stroke-width="1.00001"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>

                      <span class="text-xs font-medium">系統</span>
                    </div>
                    <span class="check-mark-icon system-theme">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12.2121 5L5.87879 11.3333L3 8.45455"
                          stroke-width="1.2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </span>
                  </button>
                  <button
                    id="toggle-theme-button"
                    data-theme="light"
                    class="flex flex-row items-center justify-between gap-1 px-2 py-2 border-b border-solid border-slate-100 dark:border-slate-800 stroke-slate-700 dark:stroke-slate-200 text-slate-800 dark:text-slate-100"
                  >
                    <div class="flex flex-row items-center gap-1">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8.0001 10.3998C9.32559 10.3998 10.4001 9.32529 10.4001 7.99982C10.4001 6.67436 9.32559 5.59985 8.0001 5.59985C6.67462 5.59985 5.6001 6.67436 5.6001 7.99982C5.6001 9.32529 6.67462 10.3998 8.0001 10.3998Z"
                          stroke-width="1.19999"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M8 2V3.19998"
                          stroke-width="1.19999"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M8 12.8V14"
                          stroke-width="1.19999"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M3.75806 3.75781L4.60406 4.6038"
                          stroke-width="1.19999"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M11.396 11.3958L12.242 12.2417"
                          stroke-width="1.19999"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M2 7.99976H3.2"
                          stroke-width="1.19999"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M12.8 7.99976H14.0001"
                          stroke-width="1.19999"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M4.60406 11.3958L3.75806 12.2417"
                          stroke-width="1.19999"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M12.242 3.75781L11.396 4.6038"
                          stroke-width="1.19999"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>

                      <span class="text-xs font-medium">明亮</span>
                    </div>
                    <span class="check-mark-icon light-theme">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12.2121 5L5.87879 11.3333L3 8.45455"
                          stroke-width="1.2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </span>
                  </button>
                  <button
                    id="toggle-theme-button"
                    data-theme="dark"
                    class="flex flex-row items-center justify-between gap-1 px-2 py-2 stroke-slate-700 dark:stroke-slate-200 text-slate-800 dark:text-slate-100"
                  >
                    <div class="flex flex-row items-center gap-1">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8 3C7.33696 3.66304 6.96447 4.56232 6.96447 5.5C6.96447 6.43768 7.33696 7.33696 8 8C8.66304 8.66304 9.56232 9.03553 10.5 9.03553C11.4377 9.03553 12.337 8.66304 13 8C13 8.9889 12.7068 9.9556 12.1573 10.7778C11.6079 11.6001 10.827 12.241 9.91342 12.6194C8.99979 12.9978 7.99446 13.0969 7.02455 12.9039C6.05465 12.711 5.16373 12.2348 4.46447 11.5355C3.76521 10.8363 3.289 9.94535 3.09608 8.97545C2.90315 8.00555 3.00217 7.00021 3.3806 6.08658C3.75904 5.17295 4.39991 4.39206 5.22215 3.84265C6.0444 3.29324 7.01109 3 8 3Z"
                          stroke-width="1.11111"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>

                      <span class="text-xs font-medium">深色</span>
                    </div>
                    <span class="check-mark-icon dark-theme">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12.2121 5L5.87879 11.3333L3 8.45455"
                          stroke-width="1.2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </nav>
        </header>
        <section
          id="portal-bg"
          class="w-full bg-white dark:bg-slate-900 shadow-inner"
        >
          <div
            id="portal-bg-gradient"
            class="pt-8 pb-8 md:pt-14 md:pb-6 min-h-[240px] md:min-h-[260px]"
          >
            <div
              class="mx-auto max-w-5xl px-4 md:px-8 flex flex-col items-center sm:items-start"
            >
              <h1
                class="text-2xl md:text-4xl text-slate-900 dark:text-white font-semibold leading-normal"
              ></h1>
              <p
                class="text-slate-600 dark:text-slate-200 text-center text-lg leading-normal pt-4 pb-4"
              >
                在此搜索文章或瀏覽以下類別。
              </p>
              <div id="search-wrap" class="w-full"></div>
            </div>
          </div>
        </section>

        <div
          class="max-w-5xl w-full flex flex-col flex-grow mx-auto py-8 px-4 md:px-8 gap-6"
        >
          <div>
            <section class="flex flex-col w-full h-full lg:container">
              <div
                class="flex flex-col gap-5 px-3 py-5 border border-solid rounded-lg border-slate-100 dark:border-slate-800"
              >
                <div class="flex items-center justify-between w-full">
                  <div class="flex flex-col items-start gap-1">
                    <div class="flex flex-row items-center gap-2 px-2">
                      <h3
                        class="text-xl font-semibold leading-relaxed text-slate-800 dark:text-slate-50"
                      >
                        精選文章
                      </h3>
                    </div>
                  </div>
                </div>
                <div
                  class="grid grid-cols-1 gap-2 md:grid-cols-2 gap-x-2 gap-y-2"
                >
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1751623667-"
                  >
                    <div
                      id="category-item"
                      class="flex items-start justify-between gap-6 px-2 py-1 rounded-lg"
                    >
                      如何聯係我們
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1752482735-"
                  >
                    <div
                      id="category-item"
                      class="flex items-start justify-between gap-6 px-2 py-1 rounded-lg"
                    >
                      如何選擇適合自己的套餐
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1752033494-"
                  >
                    <div
                      id="category-item"
                      class="flex items-start justify-between gap-6 px-2 py-1 rounded-lg"
                    >
                      如何進行個性化配置
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1752058213-"
                  >
                    <div
                      id="category-item"
                      class="flex items-start justify-between gap-6 px-2 py-1 rounded-lg"
                    >
                      什麼是Talka
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1752047993-"
                  >
                    <div
                      id="category-item"
                      class="flex items-start justify-between gap-6 px-2 py-1 rounded-lg"
                    >
                      如何創建收件箱
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1752056763-"
                  >
                    <div
                      id="category-item"
                      class="flex items-start justify-between gap-6 px-2 py-1 rounded-lg"
                    >
                      如何進行對話
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                </div>
              </div>
            </section>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-6">
            <section class="flex flex-col w-full h-full lg:container">
              <div
                id="category-block"
                class="flex flex-col gap-8 h-full border border-solid border-slate-100 dark:border-slate-800 py-5 px-3 rounded-lg"
              >
                <div class="flex items-center justify-between w-full">
                  <div class="flex flex-col items-start gap-1">
                    <div class="flex flex-row items-center gap-2 px-1">
                      <h3
                        id="category-name"
                        class="text-xl text-slate-800 dark:text-slate-50 font-semibold leading-relaxed hover:cursor-pointer pl-1"
                      >
                        <a href="/hc/description/en/categories/article">
                          常見問題
                        </a>
                      </h3>
                    </div>
                  </div>
                </div>
                <div class="flex flex-col gap-2 flex-grow -mt-4">
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1751623667-"
                  >
                    <div
                      id="category-item"
                      class="flex justify-between hover:cursor-pointer items-start py-1 rounded-lg gap-6 px-2"
                    >
                      如何聯係我們
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1752033494-"
                  >
                    <div
                      id="category-item"
                      class="flex justify-between hover:cursor-pointer items-start py-1 rounded-lg gap-6 px-2"
                    >
                      如何進行個性化配置
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1752047993-"
                  >
                    <div
                      id="category-item"
                      class="flex justify-between hover:cursor-pointer items-start py-1 rounded-lg gap-6 px-2"
                    >
                      如何創建收件箱
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1752056763-"
                  >
                    <div
                      id="category-item"
                      class="flex justify-between hover:cursor-pointer items-start py-1 rounded-lg gap-6 px-2"
                    >
                      如何進行對話
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                  <a
                    class="leading-7 text-slate-700 dark:text-slate-100"
                    href="/hc/description/articles/1752058213-"
                  >
                    <div
                      id="category-item"
                      class="flex justify-between hover:cursor-pointer items-start py-1 rounded-lg gap-6 px-2"
                    >
                      什麼是Talka
                      <span class="flex items-center font-normal mt-1.5">
                        <svg
                          class="w-4 h-4 fill-current text-slate-500 dark:text-slate-400"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
                          />
                        </svg>
                      </span>
                    </div>
                  </a>
                </div>
                <div class="flex justify-between flex-row items-center px-2">
                  <div class="flex flex-row items-center gap-1">
                    <div class="flex flex-row items-center gap-1">
                      <div class="flex flex-row items-center -space-x-2">
                        <div
                          class="w-5 h-5 rounded-full [&>svg]:opacity-70 border border-solid fill-white dark:fill-slate-900 border-white dark:border-slate-900 flex justify-center items-center"
                          style="background-color: #e19191"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="12"
                            height="12"
                            viewBox="0 0 12 12"
                          >
                            <path
                              fill=""
                              d="M6 1a2 2 0 1 0 0 4a2 2 0 0 0 0-4Zm2.5 5h-5A1.5 1.5 0 0 0 2 7.5c0 1.116.459 2.01 1.212 2.615C3.953 10.71 4.947 11 6 11c1.053 0 2.047-.29 2.788-.885C9.54 9.51 10 8.616 10 7.5A1.5 1.5 0 0 0 8.5 6Z"
                            />
                          </svg>
                        </div>
                      </div>

                      <span
                        class="text-sm font-medium text-slate-600 dark:text-slate-400"
                      >
                        1 作者
                      </span>
                    </div>

                    <span class="text-slate-600 dark:text-slate-400">•</span>
                    <span
                      class="text-sm font-medium text-slate-600 dark:text-slate-400"
                    >
                      6 文章
                    </span>
                  </div>
                  <div>
                    <a
                      href="/hc/description/en/categories/article"
                      class="flex flex-row items-center text-sm font-medium text-slate-600 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-100"
                    >
                      查看全部
                    </a>
                  </div>
                </div>
              </div>
            </section>
          </div>
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-x-6 gap-y-6"></div>
        </div>

        <footer class="pt-16 pb-8 flex flex-col items-center justify-center">
          <div class="mx-auto max-w-2xl text-center py-2">
            <div class="flex items-center gap-2">
              <img
                class="w-4 h-4"
                alt="Talka"
                src="/brand-assets/logo_thumbnail.svg"
              />
              <p
                class="text-slate-700 dark:text-slate-300 text-sm font-medium text-center"
              >
                由以下技術支持

                <a
                  class="hover:underline"
                  href=""
                  target="_blank"
                  rel="noopener noreferrer nofoll/ow"
                  >Talka</a
                >
              </p>
            </div>
          </div>
        </footer>
      </main>
    </div>
  </body>
  <style>
    html.dark {
      --dynamic-portal-bg: url(/assets/images/hc/hexagon-dark.svg)
        color-mix(in srgb, #2781f6 20%, black);
      --dynamic-portal-bg-gradient: linear-gradient(
        to bottom,
        transparent,
        #151718
      );
      --dynamic-hover-bg-color: color-mix(in srgb, #2781f6 5%, #1b1b1b);
    }

    html.light {
      --dynamic-portal-bg: url(/assets/images/hc/hexagon-light.svg)
        color-mix(in srgb, #2781f6 20%, white);
      --dynamic-portal-bg-gradient: linear-gradient(
        to bottom,
        transparent,
        white
      );
      --dynamic-hover-bg-color: color-mix(in srgb, #2781f6 5%, #f9f9f9);
    }

    /* Portal background */
    #portal-bg {
      background: var(--dynamic-portal-bg);
    }
    /* Portal background gradient */
    #portal-bg-gradient {
      background-image: var(--dynamic-portal-bg-gradient);
    }
    /* Category block item hover color */
    #category-item:hover {
      background-color: var(--dynamic-hover-bg-color);
    }

    /* Header section */
    #header-action-button:hover,
    #toggle-appearance:hover,
    #toggle-theme-button:hover {
      color: var(--dynamic-hover-color);
      stroke: var(--dynamic-hover-color);
    }
    #category-block:hover {
      border-color: var(--dynamic-hover-color);
    }
    #category-block:hover #category-name {
      color: var(--dynamic-hover-color);
    }
  </style>

  <script></script>

  <script></script>
</html>
