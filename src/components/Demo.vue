<script setup>
import { ref, watch } from "vue";

const showLightbox = ref(false);
const videoUrl = ref("");

watch(showLightbox, (newValue) => {
  if (newValue) {
    // 当 lightbox 打开时，禁止页面滚动
    document.body.style.overflow = "hidden";
  } else {
    // 当 lightbox 关闭时，恢复页面滚动
    document.body.style.overflow = "";
  }
});

function openLightbox() {
  videoUrl.value = "https://player.vimeo.com/video/948295359?h=5444340a1c";
  showLightbox.value = true;
}

function closeLightbox() {
  showLightbox.value = false;
}
</script>

<template>
  <section class="demo">
    <div class="w-layout-blockcontainer demo-container centered w-container">
      <div class="thumbnail-demo">
        <div class="w-inline-block" @click="openLightbox">
          <img
            src="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/664dddd0a9e14945763a4c17_demo%20thumbnail%209.png"
            loading="lazy"
            width="628.5"
            sizes="(max-width: 767px) 90vw, 628.5px"
            alt=""
            srcset="
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/664dddd0a9e14945763a4c17_demo%20thumbnail%209-p-500.png   500w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/664dddd0a9e14945763a4c17_demo%20thumbnail%209-p-800.png   800w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/664dddd0a9e14945763a4c17_demo%20thumbnail%209-p-1080.png 1080w,
              https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/664dddd0a9e14945763a4c17_demo%20thumbnail%209.png        1257w
            "
            class="thumbnail"
          />
          <div class="play-button-overlay">
            <!-- <div class="play-icon"></div> -->
          </div>
        </div>
      </div>

      <!-- 自定义 Lightbox -->
      <div
        v-if="showLightbox"
        class="custom-lightbox-backdrop"
        @click.self="closeLightbox"
      >
        <div class="custom-lightbox-close" @click="closeLightbox"></div>
        <div class="custom-lightbox-container">
          <div class="custom-lightbox-content">
            <div class="custom-lightbox-view">
              <iframe
                class="embedly-embed"
                :src="videoUrl"
                width="940"
                height="529"
                scrolling="no"
                title="Vimeo embed"
                frameborder="0"
                allow="autoplay; fullscreen; encrypted-media; picture-in-picture;"
                allowfullscreen="true"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.demo {
  padding: 80px 0 0;
  position: relative;
  display: block;
  overflow: visible;
}

.demo-container {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 30px;
}

.centered {
  text-align: center;
}

.w-container {
  margin-left: auto;
  margin-right: auto;
  max-width: 940px;
}

.w-layout-blockcontainer {
  max-width: 940px;
  margin-left: auto;
  margin-right: auto;
}

.thumbnail-demo {
  position: relative;
  cursor: pointer;
  max-width: 940px;
  width: 100%;
  margin-bottom: 20px;
  margin-left: auto;
  margin-right: auto;
  display: inline-block;
}

.w-inline-block {
  display: inline-block;
  position: relative;
  cursor: pointer;
  max-width: 100%;
}

.thumbnail {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 10px;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 0.9;
  }
}

/* 播放按钮样式 */
.play-button-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.play-icon {
  width: 80px;
  height: 80px;
  background-color: rgba($color-black, 0.7);
  border-radius: 50%;
  position: relative;

  &:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 55%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 25px solid $color-white;
  }
}

/* 自定义 Lightbox 样式 */
.custom-lightbox-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999; /* 使用非常高的 z-index 值 */
  background-color: rgba($color-black, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw; /* 确保宽度是视口宽度 */
  height: 100vh; /* 确保高度是视口高度 */
}

.custom-lightbox-container {
  position: relative;
  width: 100%;
  max-width: 940px;
  padding: 20px;
}

.custom-lightbox-content {
  position: relative;
}

.custom-lightbox-view {
  position: relative;
  width: 100%;
  height: 0;
  padding-top: 56.28%;

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 4px;
  }
}

.custom-lightbox-close {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 2.6em;
  height: 2.6em;
  cursor: pointer;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMTggMTciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxNyI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMCAwaDd2LTdoNXY3aDd2NWgtN3Y3aC01di03aC03eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDd2LTdoM3Y3aDd2M2gtN3Y3aC0zdi03aC03eiIgZmlsbD0iI2ZmZiIvPjwvZz48L3N2Zz4=");
  background-size: 18px;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.8;
  transition: opacity 0.3s ease;
  z-index: 10000; /* 确保关闭按钮在最上层 */

  &:hover {
    opacity: 1;
  }
}

@include mobile-only {
  .demo {
    padding: 60px 0;
  }

  .demo-container {
    padding: 0 20px;
  }

  .custom-lightbox-container {
    padding: 10px;
  }

  .play-icon {
    width: 60px;
    height: 60px;
  }
}
</style>
