<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-04-08 10:26:18
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-04-09 17:52:58
 * @FilePath     : /src/views/Home.vue
 * @Description  : 首页视图
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-04-08 10:26:18
-->
<script setup>
import { defineAsyncComponent } from "vue";

// 首屏关键组件 - 保持同步加载
import AnnouncementBar from "@/components/AnnouncementBar.vue";
import Hero from "@/components/Hero.vue";

// 非首屏组件 - 懒加载
const Demo = defineAsyncComponent(() => import("@/components/Demo.vue"));
const How = defineAsyncComponent(() => import("@/components/How.vue"));
const Price = defineAsyncComponent(() => import("@/components/Price.vue"));
const GlobalTeam = defineAsyncComponent(() =>
  import("@/components/GlobalTeam.vue")
);
const Features = defineAsyncComponent(() =>
  import("@/components/Features.vue")
);
const CallRecording = defineAsyncComponent(() =>
  import("@/components/CallRecording.vue")
);
const ContactModal = defineAsyncComponent(() =>
  import("@/components/ContactModal.vue")
);
const CallToAction = defineAsyncComponent(() =>
  import("@/components/CallToAction.vue")
);
</script>

<template>
  <AnnouncementBar />
  <Hero />
  <Demo />
  <Price />
  <How />
  <GlobalTeam />
  <Features />
  <CallRecording />
  <ContactModal />
  <CallToAction />
</template>

<style scoped></style>
