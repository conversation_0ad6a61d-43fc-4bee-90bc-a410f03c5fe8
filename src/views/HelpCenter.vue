<script setup>
import { ref, computed, watch } from "vue";
import { useI18n } from "vue-i18n";

const { locale, t } = useI18n();

const searchQuery = ref("");

// 工具函数：限制数组元素数量
const limitItems = (items, limit) => items.slice(0, limit);

const helpCenterData = ref(null);

// 根据当前语言加载对应的数据配置
const loadHelpCenterData = async () => {
  try {
    const dataModule = await import(`@/data/helpCenter-${locale.value}.js`);
    helpCenterData.value = dataModule.default;
  } catch (error) {
    console.error(`无法加载语言数据: ${locale.value}`, error);
    try {
      const defaultModule = await import(`@/data/helpCenter-zh-TW.js`);
      helpCenterData.value = defaultModule.default;
    } catch (defaultError) {
      console.error("无法加载默认语言数据", defaultError);
    }
  }
};

loadHelpCenterData();

// 监听语言变化，重新加载数据
watch(locale, () => {
  loadHelpCenterData();
});

const searchPlaceholder = computed(() => {
  return helpCenterData.value?.searchPlaceholder;
});

// 判断section是否有有效的footer内容
const hasValidFooter = (section) => {
  return (
    section.footer &&
    (section.footer.stats || section.footer.viewAll || section.footer.custom)
  );
};

// 搜索辅助函数：检查section标题是否匹配
const isSectionTitleMatch = (section, query) => {
  return section.title.toLowerCase().includes(query);
};

// 搜索辅助函数：过滤匹配的items
const getMatchedItems = (items, query) => {
  return items.filter((item) => item.title.toLowerCase().includes(query));
};

// 搜索辅助函数：处理单个section的搜索结果
const processSection = (section, query, maxItems) => {
  const titleMatch = isSectionTitleMatch(section, query);
  const matchedItems = getMatchedItems(section.items, query);

  // 如果标题匹配或有匹配的items，则包含此section
  if (titleMatch || matchedItems.length > 0) {
    let displayItems;

    if (titleMatch) {
      // 标题匹配：显示所有items（限制数量）
      displayItems = limitItems(section.items, maxItems);
    } else {
      // 内容匹配：只显示匹配的items（限制数量）
      displayItems = limitItems(matchedItems, maxItems);
    }

    return {
      ...section,
      displayItems,
    };
  }

  return null;
};

// 搜索辅助函数：获取所有sections的默认显示（无搜索时）
const getDefaultSections = (sections, globalMaxItems) => {
  return sections.map((section) => {
    // 优先使用section自己的maxItems，否则使用全局设置
    const sectionMaxItems = section.maxItems || globalMaxItems;
    return {
      ...section,
      displayItems: limitItems(section.items, sectionMaxItems),
    };
  });
};

// 搜索功能：过滤内容区域
const filteredSections = computed(() => {
  if (!helpCenterData.value?.sections) return [];

  const query = searchQuery.value.trim().toLowerCase();
  const globalMaxItems = helpCenterData.value.maxItemsPerSection || 5;
  const sections = helpCenterData.value.sections;

  // 无搜索词：返回默认显示
  if (!query) {
    return getDefaultSections(sections, globalMaxItems);
  }

  // 有搜索词：分步处理每个section
  const processedSections = sections.map((section) => {
    // 优先使用section自己的maxItems，否则使用全局设置
    const sectionMaxItems = section.maxItems || globalMaxItems;
    return processSection(section, query, sectionMaxItems);
  });

  // 移除不匹配的sections（null值）
  const matchedSections = processedSections.filter(Boolean);

  return matchedSections;
});

// 搜索结果统计
const searchResultsCount = computed(() => {
  if (!searchQuery.value.trim()) return 0;

  let count = 0;
  filteredSections.value.forEach((section) => {
    count += section.displayItems.length;
  });
  return count;
});

// 是否显示搜索结果提示
const showSearchResults = computed(() => {
  return searchQuery.value.trim().length > 0;
});
</script>

<template>
  <div class="help-center">
    <!-- 搜索区域 -->
    <section class="search-section">
      <div class="search-box">
        <input
          v-model="searchQuery"
          type="text"
          class="search-input"
          :placeholder="searchPlaceholder"
        />
        <button class="search-button">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M21 21L16.514 16.506M19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </section>

    <!-- 搜索结果提示 -->
    <div v-if="showSearchResults" class="search-results-info">
      <div v-if="searchResultsCount > 0" class="results-count">
        {{
          $t("helpCenter.searchResults.found", { count: searchResultsCount })
        }}
      </div>
      <div v-else class="no-results">
        {{ $t("helpCenter.searchResults.notFound", { query: searchQuery }) }}
      </div>
    </div>

    <!-- 内容区域 -->
    <section class="content-section">
      <!-- 动态内容区域 -->
      <div
        v-for="section in filteredSections"
        :key="section.id"
        class="content-area"
      >
        <h2 class="area-title">{{ section.title }}</h2>
        <ul
          class="link-list"
          :class="{
            'has-footer': hasValidFooter(section),
          }"
        >
          <li v-for="item in section.displayItems" :key="item.title">
            <a :href="item.href" class="link-item" target="_blank">
              <span class="link-text">{{ item.title }}</span>
              <svg
                class="arrow-icon"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M9 18L15 12L9 6"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </a>
          </li>
        </ul>

        <!-- 动态底部内容 -->
        <div v-if="hasValidFooter(section)" class="section-footer">
          <!-- 左侧：统计信息 -->
          <div v-if="section.footer.stats" class="stats-info">
            <svg
              class="stats-icon"
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="none"
            >
              <circle cx="12" cy="12" r="10" fill="#ff4444" />
            </svg>
            <span class="stats-text">{{ section.footer.stats }}</span>
          </div>

          <!-- 自定义内容（如果没有统计信息） -->
          <div v-else-if="section.footer.custom" class="custom-footer">
            {{ section.footer.custom }}
          </div>

          <!-- 右侧：查看全部链接 -->
          <div v-if="section.footer.viewAll" class="view-all">
            <a
              :href="section.footer.viewAll.href || '#'"
              class="view-all-link"
              target="_blank"
            >
              {{ section.footer.viewAll.text }}
            </a>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/abstracts/variables" as *;
@use "@/styles/abstracts/mixins" as *;

.help-center {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: #ffffff;
  padding: 80px 20px 40px;
  max-width: 1200px;
  margin: 0 auto;

  // 搜索区域
  .search-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 60px;

    .search-title {
      font-size: 18px;
      font-weight: 400;
      color: #333333;
      text-align: center;
      margin: 0 0 24px 0;
    }

    .search-box {
      position: relative;
      width: 100%;
      max-width: 600px;

      .search-input {
        width: 100%;
        height: 48px;
        padding: 12px 50px 12px 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 16px;
        background-color: #ffffff;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;

        &:focus {
          outline: none;
          border-color: #333333;
          box-shadow: 0 0 0 3px rgba(51, 51, 51, 0.1);
        }

        &::placeholder {
          color: #999999;
        }
      }

      .search-button {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
        color: #666666;
        padding: 4px;
        border-radius: 4px;
        transition: color 0.3s ease;

        &:hover {
          color: #333333;
        }
      }
    }
  }

  // 搜索结果提示
  .search-results-info {
    text-align: center;
    margin-bottom: 32px;

    .results-count {
      font-size: 14px;
      color: #666666;
      padding: 8px 16px;
      background-color: #f5f5f5;
      border-radius: 20px;
      display: inline-block;
    }

    .no-results {
      font-size: 16px;
      color: #999999;
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
    }
  }

  // 内容区域
  .content-section {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;

    .content-area {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 24px;
      background-color: #ffffff;
      flex: 0 0 calc(50% - 12px); // 固定宽度，每行最多两个
      min-width: 300px; // 最小宽度，确保内容可读性

      // 当只有一个元素在行中时，限制最大宽度
      &:only-child {
        max-width: 600px;
        flex: 0 0 auto;
      }

      .area-title {
        font-size: 18px;
        font-weight: 600;
        color: #333333;
        margin: 0 0 16px 0;
        line-height: 1.2;
      }

      .link-list {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          margin-bottom: 0;

          .link-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            text-decoration: none;
            padding: 12px 0;
            transition: color 0.3s ease;

            .link-text {
              flex: 1;
            }

            .arrow-icon {
              color: #999999;
              transition: color 0.3s ease;
            }

            &:hover {
              color: #333333;

              .arrow-icon {
                color: #333333;
              }
            }
          }
        }
      }

      // 当有footer时，给link-list添加下边距
      .link-list.has-footer {
        margin-bottom: 20px;
      }

      // 通用底部区域样式
      .section-footer {
        padding-top: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .stats-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .stats-icon {
            flex-shrink: 0;
          }

          .stats-text {
            font-size: 12px;
            color: #999999;
          }
        }

        .view-all {
          margin-left: auto; // 确保始终在右侧
          text-align: right;

          .view-all-link {
            font-size: 14px;
            color: #007bff;
            text-decoration: none;
            transition: color 0.3s ease;

            &:hover {
              color: #0056b3;
              text-decoration: underline;
            }
          }
        }

        .custom-footer {
          font-size: 12px;
          color: #999999;
        }
      }
    }
  }

  // 移动端适配
  @include mobile-only {
    padding: 60px 16px 40px;

    .search-section {
      margin-bottom: 40px;

      .search-title {
        font-size: 16px;
      }

      .search-box {
        .search-input {
          height: 44px;
          font-size: 14px;
        }
      }
    }

    .content-section {
      flex-direction: column;
      gap: 20px;

      .content-area {
        flex: none !important;
        max-width: none !important; // 移动端取消宽度限制
        padding: 20px;

        .area-title {
          font-size: 16px;
        }

        .link-list {
          li {
            .link-item {
              font-size: 13px;
              padding: 10px 0;
            }
          }
        }

        .section-footer {
          .stats-info {
            .stats-text {
              font-size: 11px;
            }
          }

          .view-all {
            .view-all-link {
              font-size: 13px;
            }
          }

          .custom-footer {
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>
