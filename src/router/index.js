/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-23 12:15:03
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 16:29:40
 * @FilePath     : /src/router/index.js
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-23 12:15:03
 */
import { createRouter, createWebHistory } from "vue-router";
import InfoCenter from "@/components/InfoCenter.vue";

const routes = [
  {
    path: "/",
    name: "Home",
    component: () => import("@/views/Home.vue"),
  },
  {
    path: "/info-center",
    name: "InfoCenter",
    component: InfoCenter,
  },
  {
    path: "/help-center",
    name: "HelpCenter",
    component: () => import("@/views/HelpCenter.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
