{"name": "talka-clone", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "phone": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"aos": "^2.3.4", "pinia": "^2.1.7", "sass": "^1.77.8", "vue": "^3.4.29", "vue-i18n": "10.0.4", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "autoprefixer": "^10.4.21", "chrome-launcher": "^1.2.0", "critical": "^7.2.1", "cssnano": "^7.0.7", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-webp": "^8.0.0", "lighthouse": "^12.7.1", "postcss": "^8.5.6", "postcss-px-to-viewport": "^1.1.1", "terser": "^5.39.0", "vite": "^6.0.1", "vite-plugin-imagemin": "^0.6.1"}}