<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-05 14:43:39
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-07 14:44:12
 * @FilePath     : /index.html
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-05 14:43:39
-->

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Talka</title>
    <meta content="Talka" property="og:title" />
    <meta
      content="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ce1bf620b35a26dcdf98d0_open%20graph%20talka.png"
      property="og:image"
    />
    <meta content="Talka" property="twitter:title" />
    <meta
      content="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/65ce1bf620b35a26dcdf98d0_open%20graph%20talka.png"
      property="twitter:image"
    />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
      name="viewport"
    />
    <!-- 关键CSS内联 -->
    <style>
      /* 关键渲染路径CSS */
      body {
        margin: 0;
        padding: 0;
        font-family: "Poppins", sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #fff;
      }
      /* 防止布局偏移 */
      .optimized-image {
        display: block;
        max-width: 100%;
        height: auto;
      }

      /* 加载动画 */
      .loading-placeholder {
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% {
          background-position: 200% 0;
        }
        100% {
          background-position: -200% 0;
        }
      }
    </style>

    <!-- 延迟加载非关键CSS -->
    <link
      href="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/css/talka-dev.webflow.cebf4df81.css"
      rel="preload"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript>
      <link
        href="https://cdn.prod.website-files.com/61697b680aeeaeba1f87f13b/css/talka-dev.webflow.cebf4df81.css"
        rel="stylesheet"
        type="text/css"
      />
    </noscript>
    <!-- 字体预连接 - 仅在实际使用时保留 -->
    <link href="https://fonts.googleapis.com" rel="preconnect" />
    <link
      href="https://fonts.gstatic.com"
      rel="preconnect"
      crossorigin="anonymous"
    />
    <!-- 优化字体加载 -->
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;800&display=swap"
      rel="stylesheet"
    />

    <!-- 预加载关键资源 -->
    <link
      rel="preload"
      href="/src/assets/logo_dark.png"
      as="image"
      type="image/png"
    />
    <link
      rel="preload"
      href="/src/assets/logo_light.png"
      as="image"
      type="image/png"
    />
    <link
      rel="preload"
      href="/src/assets/how_img1_srcset_pc1600.png"
      as="image"
      type="image/png"
    />

    <!-- 预加载关键脚本 -->
    <link rel="modulepreload" href="/src/main.js" />

    <link
      href="/src/assets/tab_logo.png"
      rel="shortcut icon"
      type="image/x-icon"
    />
    <link href="/src/assets/tab_logo.png" rel="apple-touch-icon" />
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>

    <script>
      (function (d, t) {
        var BASE_URL = "https://www.talkachat.com";
        var g = d.createElement(t),
          s = d.getElementsByTagName(t)[0];
        g.src = BASE_URL + "/packs/js/sdk.js";
        g.defer = true;
        g.async = true;
        s.parentNode.insertBefore(g, s);
        g.onload = function () {
          window.talkaSDK.run({
            websiteToken: "1ccUx2NmeURbPPW9TXM4RcZ9",
            baseUrl: BASE_URL,
          });
        };
      })(document, "script");
    </script>
  </body>
</html>
