#!/bin/bash

# 零停机部署脚本
set -e

# 配置变量
SSH_KEY="/Users/<USER>/Desktop/customer_sys/aws/官网/Official_website.pem"
SERVER_USER="ec2-user"
SERVER_IP="************"
SERVER_PATH="/home/<USER>/projects/talka.tw"
LOCAL_DIST="./dist"

echo "🚀 开始部署到新服务器 ${SERVER_IP}..."

# 构建项目
echo "📦 构建项目..."
npm run build

# 使用 rsync 增量同步到临时目录
echo "📤 增量同步文件到临时目录..."
rsync -avz --delete -e "ssh -i ${SSH_KEY}" ${LOCAL_DIST}/ ${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/dist_new/

# 原子切换
echo "🔄 执行原子切换..."
ssh -i ${SSH_KEY} ${SERVER_USER}@${SERVER_IP} "
cd ${SERVER_PATH}

# 清理旧备份，只保留最近1个（加上即将创建的备份，总共2个）
echo '清理旧备份...'
ls -dt dist_backup_* 2>/dev/null | tail -n +2 | xargs -r rm -rf

if [ -d dist ]; then
    echo '备份当前版本...'
    mv dist dist_backup_\$(date +%Y%m%d_%H%M%S)
fi

echo '切换到新版本...'
mv dist_new dist
echo '✅ 服务器端切换完成!'
"

echo "🎉 部署成功完成!"
echo "🌐 访问 https://talka.tw 查看更新"