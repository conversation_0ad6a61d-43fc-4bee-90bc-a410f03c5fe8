<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-17 14:16:52
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-22 13:54:03
 * @FilePath     : /README.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-03-17 14:16:52
-->

# Talka.tw - 客服官网

该项目使用 Vue 3 + Vite + SCSS 进行开发，是 Talka 客服系统的官方网站。

## 项目简介

talka.tw 客服官网
参考: [Talka](https://www.talka.ai/)

## 技术栈

- Vue 3 (使用 `<script setup>` 语法)
- Vite 构建工具
- SCSS 样式预处理器
- Vue I18n 国际化
- AOS 滚动动画
- Pinia 状态管理

## 功能特点

- 响应式设计，适配各种设备
- 多语言支持（中文、英文）
- 性能优化

## 安装与运行

### 前提条件

- Node.js (推荐 v14.0.0 或更高版本)
- npm 或 pnpm 包管理器

### 安装步骤

1. 克隆仓库并进入项目目录

```bash
cd talka.tw
```

2. 安装依赖

```bash
npm install
# 或者
pnpm install
```

3. 启动开发服务器

```bash
npm run dev
# 或者
pnpm dev
```

4. 在手机设备上测试

```bash
npm run phone
# 或者
pnpm phone
```

5. 构建生产版本

```bash
npm run build
# 或者
pnpm build
```

## 项目结构

```plaintext
talka.tw/
├── public/          # 静态资源
├── src/             # 源代码
│   ├── assets/      # 资源文件
│   │   ├── how/     # 使用说明相关图片
│   │   ├── lottie/  # Lottie 动画文件
│   │   └── price/   # 价格相关图片
│   ├── components/  # 组件
│   │   └── price/   # 价格相关组件
│   ├── locales/     # 国际化文件
│   │   ├── en/      # 英文翻译
│   │   └── zh-TW/   # 繁体中文翻译
│   ├── styles/      # SCSS样式文件
│   │   ├── abstracts/  # 变量、混合器等
│   │   ├── base/       # 基础样式
│   │   └── components/ # 组件样式
│   ├── ui/          # UI 相关资源
│   ├── App.vue      # 根组件
│   └── main.js      # 入口文件
├── performance-reports/ # 性能报告
├── .gitignore       # Git忽略文件
├── deploy.sh        # 零停机部署脚本
├── index.html       # HTML模板
├── package.json     # 项目配置
├── vite.config.js   # Vite配置
└── README.md        # 项目说明
```

## 部署

项目使用零停机部署脚本 `deploy.sh` 进行自动化部署，确保在更新过程中网站不会出现服务中断。

### 使用部署脚本

```bash
# 确保脚本有执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

部署脚本会自动执行以下操作：

1. 构建项目
2. 将构建产物增量同步到服务器的临时目录
3. 在服务器端执行原子切换，包括备份当前版本和切换到新版本

### 服务器信息

- **生产服务器:** 13.212.0.148
- **部署路径:** /home/<USER>/projects/talka.tw

## 性能优化

项目包含性能优化相关工作，主要涉及：

- 图片优化（使用 vite-plugin-imagemin）
- 主线程优化
- 关键 CSS 提取（使用 critical）
- 代码压缩（使用 terser）

性能优化报告可在 `performance-reports` 目录中查看。
