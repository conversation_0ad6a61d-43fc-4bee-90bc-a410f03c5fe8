/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-15 14:16:48
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 13:58:27
 * @FilePath     : /vite.config.js
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-03-15 14:16:48
 */
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import viteImagemin from "vite-plugin-imagemin";
import postcssPixelToViewport from "postcss-px-to-viewport";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 图片优化插件
    viteImagemin({
      gifsicle: { optimizationLevel: 7, interlaced: false },
      mozjpeg: { quality: 80, progressive: true },
      pngquant: { quality: [0.6, 0.8], speed: 4 },
      svgo: {
        plugins: [
          { name: "removeViewBox", active: false },
          { name: "removeEmptyAttrs", active: false },
        ],
      },
      webp: { quality: 80 },
    }),
  ],
  // base: "./", // 使用相对路径
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  build: {
    // 启用 gzip 压缩
    reportCompressedSize: true,

    // 启用CSS代码分割
    cssCodeSplit: true,

    // 设置更大的 chunk 大小警告阈值
    chunkSizeWarningLimit: 1000,

    // 启用源码映射（仅开发环境）
    sourcemap: process.env.NODE_ENV === "development",

    // 优化构建输出
    rollupOptions: {
      output: {
        // 按类型分目录
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split(".");
          const ext = info[info.length - 1];
          if (/\.(png|jpe?g|gif|svg|webp|ico)$/.test(assetInfo.name)) {
            return `assets/img/[name]-[hash][extname]`;
          }
          if (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name)) {
            return `assets/fonts/[name]-[hash][extname]`;
          }
          if (/\.css$/.test(assetInfo.name)) {
            return `assets/css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
        chunkFileNames: "assets/js/[name]-[hash].js",
        entryFileNames: "assets/js/[name]-[hash].js",

        // 更精细的代码分割
        manualChunks: {
          // Vue 核心
          "vue-vendor": ["vue"],

          // 国际化
          "vue-i18n": ["vue-i18n"],

          // AOS 动画
          aos: ["aos"],
        },
      },

      // 外部依赖优化 (如果使用CDN)
      // external: ['vue'], // 取消注释如果使用CDN
    },

    // 启用terser压缩选项
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ["console.log"],
      },
      mangle: {
        safari10: true,
      },
    },
  },

  // 优化依赖预构建
  optimizeDeps: {
    include: ["vue", "vue-i18n", "aos"],
    // 强制预构建
    force: false,
  },

  // 服务器配置优化
  server: {
    port: 5173,
    host: true,
  },

  // CSS 预处理器配置
  css: {
    postcss: {
      // plugins: [postcssPixelToViewport({})],
    },
    preprocessorOptions: {
      scss: {
        // 移除additionalData以避免与@use规则冲突
      },
    },
  },
});
